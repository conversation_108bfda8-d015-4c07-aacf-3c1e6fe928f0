<div class="btn-group">
	<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
		Config display fields <span class="caret"></span>
	</button>
	<ul class="dropdown-menu keep-dropdown" id="select-fields">
		<li><a href="#" class="link" id="update-list-enable-fields">Update list display fields</a></li>
		<li class="divider"></li>
        <?php asort(CraService::$list_all_fields) ?>
		<?php foreach(CraService::$list_all_fields as $code=>$field):?>
		<?php
		// Get user's selected fields with fallback to default fields
		$userCraFields = Yii::app()->user->craFields ? Yii::app()->user->craFields : array();
		if (empty($userCraFields)) {
			// Default fields if no user preferences are set
			$userCraFields = array('id', 'status', 'exposition', 'assigneeEmail', 'creatorEmail', 'contentFiles', 'timeline');
		}
		?>
		<li>
			<div class="checkbox">
				<label class="ui-checks">
					<input type="checkbox" value="<?php echo $code;?>" <?php if(in_array($code, $userCraFields)) echo 'checked';?>>
					<i></i> <?php echo $field;?>
				</label>
			</div>
		</li>
		<?php endforeach;?>
	</ul>
</div>