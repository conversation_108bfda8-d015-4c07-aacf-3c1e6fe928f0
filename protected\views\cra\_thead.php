<?php
	$list_queries = array();
	foreach($queries as $query){
		if($query['operator'] == "in"){
			if(!isset($list_queries[$query['field'].' '.$query['operator']])){
				$list_queries[$query['field'].' '.$query['operator']] = array();
			}
			$list_queries[$query['field'].' '.$query['operator']][] = $query['value'];
		}
		else{
			$list_queries[$query['field'].' '.$query['operator']] = $query['value'];
		}
	}
?>
<tr>
	<th class="w-thumb-sm text-center">
		<div class="checkbox no-margin">
			<label class="ui-checks">
				<input id="checkall-items" type="checkbox" value="">
				<i></i>
			</label>
		</div>
	</th>

	<!-- CRA Number Column -->
	<?php
		$class="";
		if(in_array('id ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('id DESC',$orders)){
			$class=" st-sort-descent";
		}
	?>
	<th class="column-id st-sort w-thumb-md<?php echo $class;?>">
		<div data-toggle="dropdown" class="dropdown-toogle">CRA Number</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">CRA Number
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="id ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="id DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
			</div>
		</div>
	</th>

	<!-- Content Files Column -->
	<th class="column-contentFiles st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">Content Files</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Content Files
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
		</div>
	</th>

	<!-- Date of Submitting Column -->
	<?php
		$class="";
		if(in_array('date_of_submitting ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('date_of_submitting DESC',$orders)){
			$class=" st-sort-descent";
		}
	?>
	<th class="column-dateOfSubmitting st-sort<?php echo $class;?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Date of Submitting</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Date of Submitting
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="date_of_submitting ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="date_of_submitting DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
			</div>
		</div>
	</th>

	<!-- Date of Requesting Proof Document Column -->
	<?php
		$class="";
		if(in_array('date_of_requesting_proof_document ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('date_of_requesting_proof_document DESC',$orders)){
			$class=" st-sort-descent";
		}
	?>
	<th class="column-dateOfRequestingProofDocument st-sort<?php echo $class;?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Date of Requesting Proof Document</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Date of Requesting Proof Document
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="date_of_requesting_proof_document ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="date_of_requesting_proof_document DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
			</div>
		</div>
	</th>

	<!-- Date of Receiving Proof Document Column -->
	<?php
		$class="";
		if(in_array('date_of_receiving_proof_document ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('date_of_receiving_proof_document DESC',$orders)){
			$class=" st-sort-descent";
		}
	?>
	<th class="column-dateOfReceivingProofDocument st-sort<?php echo $class;?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Date of Receiving Proof Document</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Date of Receiving Proof Document
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="date_of_receiving_proof_document ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="date_of_receiving_proof_document DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
			</div>
		</div>
	</th>

	<!-- Proof Documents Column -->
	<th class="column-proofDocuments st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">Proof Documents</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Proof Documents
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
		</div>
	</th>

	<!-- Date of STRAD Risk Assessment Column -->
	<?php
		$class="";
		if(in_array('date_of_strad_risk_assessment ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('date_of_strad_risk_assessment DESC',$orders)){
			$class=" st-sort-descent";
		}
	?>
	<th class="column-dateOfStradRiskAssessment st-sort<?php echo $class;?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Date of STRAD Risk Assessment</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Date of STRAD Risk Assessment
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="date_of_strad_risk_assessment ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="date_of_strad_risk_assessment DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
			</div>
		</div>
	</th>

	<!-- Date of Completed Column -->
	<?php
		$class="";
		if(in_array('date_of_completed ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('date_of_completed DESC',$orders)){
			$class=" st-sort-descent";
		}
	?>
	<th class="column-dateOfCompleted st-sort<?php echo $class;?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Date of Completed</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Date of Completed
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="date_of_completed ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="date_of_completed DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
			</div>
		</div>
	</th>

	<!-- Exposition Column (Combined Level and Detail) -->
	<?php
		$class="";
		// Check for sorting on either exposition_level or exposition_detail
		if(in_array('exposition_level ASC',$orders) || in_array('exposition_detail ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('exposition_level DESC',$orders) || in_array('exposition_detail DESC',$orders)){
			$class=" st-sort-descent";
		}
		$value = "";
		// Check for search on either field
		if(isset($list_queries['exposition_level like'])){
			$value = $list_queries['exposition_level like'];
		}
		elseif(isset($list_queries['exposition_detail like'])){
			$value = $list_queries['exposition_detail like'];
		}
	?>
	<th class="column-exposition st-sort<?php echo $class;?><?php if(!empty($value)) echo " st-search";?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Exposition</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Exposition
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="exposition_level ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="exposition_level DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
				<hr>
				<div>
					<p><input type="text" operator="like" name="exposition_level" class="input-search form-control rounded" value="<?php echo $value;?>" placeholder="Search by exposition level or detail"></p>
				</div>
			</div>
		</div>
	</th>

	<!-- Creator Email Column -->
	<?php
		$class="";
		if(in_array('creator_email ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('creator_email DESC',$orders)){
			$class=" st-sort-descent";
		}
		$value = "";
		if(isset($list_queries['creator_email like'])){
			$value = $list_queries['creator_email like'];
		}
	?>
	<th class="column-creatorEmail st-sort<?php echo $class;?><?php if(!empty($value)) echo " st-search";?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Creator Email</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Creator Email
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="creator_email ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="creator_email DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
				<hr>
				<div>
					<p><input type="text" operator="like" name="creator_email" class="input-search form-control rounded" value="<?php echo $value;?>"></p>
				</div>
			</div>
		</div>
	</th>

	<!-- Follower Email Column -->
	<?php
		$class="";
		if(in_array('follower_email ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('follower_email DESC',$orders)){
			$class=" st-sort-descent";
		}
		$value = "";
		if(isset($list_queries['follower_email like'])){
			$value = $list_queries['follower_email like'];
		}
	?>
	<th class="column-followerEmail st-sort<?php echo $class;?><?php if(!empty($value)) echo " st-search";?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Follower Email</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Follower Email
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="follower_email ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="follower_email DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
				<hr>
				<div>
					<p><input type="text" operator="like" name="follower_email" class="input-search form-control rounded" value="<?php echo $value;?>"></p>
				</div>
			</div>
		</div>
	</th>

	<!-- Assignee Email Column -->
	<?php
		$class="";
		if(in_array('assignee_email ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('assignee_email DESC',$orders)){
			$class=" st-sort-descent";
		}
		$value = "";
		if(isset($list_queries['assignee_email like'])){
			$value = $list_queries['assignee_email like'];
		}
	?>
	<th class="column-assigneeEmail st-sort<?php echo $class;?><?php if(!empty($value)) echo " st-search";?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Assignee Email</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Assignee Email
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="assignee_email ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="assignee_email DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
				<hr>
				<div>
					<p><input type="text" operator="like" name="assignee_email" class="input-search form-control rounded" value="<?php echo $value;?>"></p>
				</div>
			</div>
		</div>
	</th>

	<!-- Creator Full Name Column -->
	<th class="column-creatorFullName st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">Fullname of creator</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Fullname of creator
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="creator_full_name ASC" class="asort fa fa-sort-alpha-asc"></a>
					</div>
					<div class="col-xs-4">
						<a href="creator_full_name DESC" class="asort fa fa-sort-alpha-desc"></a>
					</div>
				</div>
				<hr>
				<div>
					<p><input type="text" operator="like" name="creator_full_name" class="input-search form-control rounded" value=""></p>
				</div>
			</div>
		</div>
	</th>

	<!-- Follower Full Name Column -->
	<th class="column-followerFullName st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">Fullname of follower</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Fullname of follower
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="follower_full_name ASC" class="asort fa fa-sort-alpha-asc"></a>
					</div>
					<div class="col-xs-4">
						<a href="follower_full_name DESC" class="asort fa fa-sort-alpha-desc"></a>
					</div>
				</div>
				<hr>
				<div>
					<p><input type="text" operator="like" name="follower_full_name" class="input-search form-control rounded" value=""></p>
				</div>
			</div>
		</div>
	</th>

	<!-- Assignee Full Name Column -->
	<th class="column-assigneeFullName st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">Fullname of assignee</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Fullname of assignee
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="assignee_full_name ASC" class="asort fa fa-sort-alpha-asc"></a>
					</div>
					<div class="col-xs-4">
						<a href="assignee_full_name DESC" class="asort fa fa-sort-alpha-desc"></a>
					</div>
				</div>
				<hr>
				<div>
					<p><input type="text" operator="like" name="assignee_full_name" class="input-search form-control rounded" value=""></p>
				</div>
			</div>
		</div>
	</th>

	<!-- Reference Files Column -->
	<th class="column-referencesFiles st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">Reference Files</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Reference Files
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
		</div>
	</th>

	<!-- Requests Column (request_ids) -->
	<?php
		$class="";
		if(in_array('request_ids ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('request_ids DESC',$orders)){
			$class=" st-sort-descent";
		}
	?>
	<th class="column-requestIds st-sort w-thumb-md<?php echo $class;?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Requests</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Requests
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="request_ids ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="request_ids DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
			</div>
		</div>
	</th>

	<!-- Status Column -->
	<?php
		$class="";
		if(in_array('status ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('status DESC',$orders)){
			$class=" st-sort-descent";
		}
		$value = array();
		if(isset($list_queries['status in'])){
			$value = $list_queries['status in'];
		}
	?>
	<th class="column-status st-sort w-thumb-md<?php echo $class;?><?php if(sizeof($value)>0) echo " st-search";?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Status</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Status
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="status ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="status DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
				<hr>
				<ul class="nav-checkbox-box">
					<li>
						<div class="checkbox">
							<label class="ui-checks">
								<input class="input-search" operator="in" name="status" type="checkbox" value="WAIT_FOR_MKT_SUBMISSION" <?php if(in_array("WAIT_FOR_MKT_SUBMISSION",$value)) echo 'checked';?>>
								<i></i>WAIT_FOR_MKT_SUBMISSION
							</label>
						</div>
					</li>
					<li>
						<div class="checkbox">
							<label class="ui-checks">
								<input class="input-search" operator="in" name="status" type="checkbox" value="WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN" <?php if(in_array("WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN",$value)) echo 'checked';?>>
								<i></i>WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN
							</label>
						</div>
					</li>
					<li>
						<div class="checkbox">
							<label class="ui-checks">
								<input class="input-search" operator="in" name="status" type="checkbox" value="WAIT_FOR_STRAD_RISK_ASSESSMENT" <?php if(in_array("WAIT_FOR_STRAD_RISK_ASSESSMENT",$value)) echo 'checked';?>>
								<i></i>WAIT_FOR_STRAD_RISK_ASSESSMENT
							</label>
						</div>
					</li>
					<li>
						<div class="checkbox">
							<label class="ui-checks">
								<input class="input-search" operator="in" name="status" type="checkbox" value="WAIT_FOR_APPROVAL" <?php if(in_array("WAIT_FOR_APPROVAL",$value)) echo 'checked';?>>
								<i></i>WAIT_FOR_APPROVAL
							</label>
						</div>
					</li>
					<li>
						<div class="checkbox">
							<label class="ui-checks">
								<input class="input-search" operator="in" name="status" type="checkbox" value="COMPLETED" <?php if(in_array("COMPLETED",$value)) echo 'checked';?>>
								<i></i>COMPLETED
							</label>
						</div>
					</li>
					<li>
						<div class="checkbox">
							<label class="ui-checks">
								<input class="input-search" operator="in" name="status" type="checkbox" value="CANCEL" <?php if(in_array("CANCEL",$value)) echo 'checked';?>>
								<i></i>CANCEL
							</label>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</th>

	<!-- STRAD Risk Assessment Files Column -->
	<th class="column-stradRiskAssessmentFiles st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">STRAD Risk Assessment Files</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">STRAD Risk Assessment Files
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
		</div>
	</th>

	<!-- Timeline Column -->
	<?php
		$class="";
		if(in_array('timeline ASC',$orders)){
			$class=" st-sort-ascent";
		}
		elseif(in_array('timeline DESC',$orders)){
			$class=" st-sort-descent";
		}
	?>
	<th class="column-timeline st-sort<?php echo $class;?>">
		<div data-toggle="dropdown" class="dropdown-toogle">Timeline</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Timeline
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="timeline ASC" class="asort fa fa-sort-alpha-asc<?php if($class==" st-sort-ascent") echo " selected";?>"></a>
					</div>
					<div class="col-xs-4">
						<a href="timeline DESC" class="asort fa fa-sort-alpha-desc<?php if($class==" st-sort-descent") echo " selected";?>"></a>
					</div>
				</div>
			</div>
		</div>
	</th>
</tr>
