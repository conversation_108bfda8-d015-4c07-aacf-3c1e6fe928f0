<?php
/**
 * J<PERSON>ervice represents an script for connecting to RESTFUL API
 */
class JService
{
	const PAGE_SIZE = 9;
	//public static $baseUrl='http://**************:8090/api';
	public static $baseUrl='http://localhost:3005/api';

	/**
	 * Create item
	 */
	public static function create($item){
		if(isset(Yii::app()->session['token'])){
			$curl = curl_init(self::$baseUrl."/".static::$apiModule);
			curl_setopt($curl, CURLOPT_POST, true);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						'Content-Type: application/json;charset=utf-8',
						"Authorization: Bearer ".Yii::app()->session['token']
						));
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($item));
			// Make the REST call, returning the result
			$response = curl_exec($curl);
			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			if($http_status == 200){
				return $result;
			}
			elseif($http_status == 401 && !isset($result->errorCode)){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				var_dump($response);
				exit;
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}
	
	/**
	 * Update item
	 */
	public static function update($id,$item,$operation=null){
		if(isset(Yii::app()->session['token'])){	
			$curl = curl_init(self::$baseUrl."/".static::$apiModule."/".$id."/partial");
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						'Content-Type: application/json;charset=utf-8',
						"Authorization: Bearer ".Yii::app()->session['token']
						)); 
			$data = array('updatedParts'=>$item);
			if(isset($operation)){
				$data['operation'] = $operation;
			}
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
			// Make the REST call, returning the result
			$response = curl_exec($curl);
			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			curl_close($curl);
			if($http_status == 200){
				return true;
			}
			elseif($http_status == 401){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				// Convert the result from JSON format to a PHP array
				$result = json_decode($response);
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	/**
	 * Update full item
	 */
	public static function updateFull($id,$item){
		if(isset(Yii::app()->session['token'])){	
			$curl = curl_init(self::$baseUrl."/".static::$apiModule."/".$id);
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						'Content-Type: application/json;charset=utf-8',
						"Authorization: Bearer ".Yii::app()->session['token']
						));
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($item));
			// Make the REST call, returning the result
			$response = curl_exec($curl);
			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			curl_close($curl);
			if($http_status == 200){
				return true;
			}
			elseif($http_status == 401){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				// Convert the result from JSON format to a PHP array
				$result = json_decode($response);
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}
	
	/**
	 * Delete item
	 */
	
	public static function get($id){
		if(isset(Yii::app()->session['token'])){
			$curl = curl_init(self::$baseUrl."/".static::$apiModule."/".$id);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						"Accept: application/json",
						"Authorization: Bearer ".Yii::app()->session['token']
						)); 
			// Make the REST call, returning the result
			$response = curl_exec($curl);
			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if($http_status == 200){
				return $result;
			}
			elseif($http_status == 401 && !isset($result->errorCode)){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}
	
	/**
	 * Delete item
	 */
	
	public static function delete($id){
		if(isset(Yii::app()->session['token'])){	
			$curl = curl_init(self::$baseUrl."/".static::$apiModule."/".$id);
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						"Authorization: Bearer ".Yii::app()->session['token']
						)); 
			// Make the REST call, returning the result
			$response = curl_exec($curl);
			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			curl_close($curl);
			if($http_status == 200){
				return true;
			}
			elseif($http_status == 401){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				// Convert the result from JSON format to a PHP array
				$result = json_decode($response);
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
			
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}
	
	/**
	 * Delete multi items
	 */
	
	public static function batch($list_item_ids){
		if(isset(Yii::app()->session['token'])){	
			$curl = curl_init(self::$baseUrl."/".static::$apiModule."/batch");
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						'Content-Type: application/json;charset=utf-8',
						"Authorization: Bearer ".Yii::app()->session['token']
						));
			
			curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($list_item_ids));
			// Make the REST call, returning the result
			$response = curl_exec($curl);
			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			curl_close($curl);
			if($http_status == 200){
				return true;
			}
			elseif($http_status == 401){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				// Convert the result from JSON format to a PHP array
				$result = json_decode($response);
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
			
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	public static function count($queries=array(),$query="",$filter=null){
		if(isset(Yii::app()->session['token'])){
			$query = self::createQuery($queries,$query);
			
			$data = array(
		    	"query" => ($query != "")?$query:null,
		    	"filter" => $filter
		    );	
			$curl = curl_init(self::$baseUrl."/".static::$apiModule."/count?".http_build_query($data));
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						"Authorization: Bearer ".Yii::app()->session['token']
						)); 
			// Make the REST call, returning the result
			$response = curl_exec($curl);
			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			if($http_status == 200){
				return $response;
			}
			elseif($http_status == 401){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
                // Convert the result from JSON format to a PHP array
                $result = json_decode($response);
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	/**
	 * Get all items
	 */
	public static function getAll(){
		if(isset(Yii::app()->session['token'])){
			$url = 	self::$baseUrl."/".static::$apiModule;
			$curl = curl_init($url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						"Accept: application/json",
						"Authorization: Bearer ".Yii::app()->session['token']
						)); 
			// Make the REST call, returning the result
			$response = curl_exec($curl);

			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if($http_status == 200){
				return $result;
			}
			elseif($http_status == 401 && !isset($result->errorCode)){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}


	/**
	 * Get list items
	 */
	public static function getList($page=1,$orders=array(),$queries=array(),$query="",$filter=null){
		if(isset(Yii::app()->session['token'])){
			
			$query = self::createQuery($queries,$query);
			$data = array(
		   	 	"page" => $page,
		    	"size" => self::PAGE_SIZE,
		    	"order" => (sizeof($orders)>0)?implode(",",$orders):null,
		    	"query" => ($query != "")?$query:null,
		    	"filter" => $filter
		    );
			$url = 	self::$baseUrl."/".static::$apiModule."?".http_build_query($data);
			$curl = curl_init($url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						"Accept: application/json",
						"Authorization: Bearer ".Yii::app()->session['token']
						)); 
			// Make the REST call, returning the result
			$response = curl_exec($curl);

			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if($http_status == 200){
				return $result;
			}
			elseif($http_status == 401 && !isset($result->errorCode)){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	public static function createQuery($queries,$query){
		$tmp_queries = array();
		$in_value = array();
		foreach($queries as $tmp_query){
			if($tmp_query['operator'] == "="){
				$tmp_queries[] = $tmp_query['field']." = ".$tmp_query['value'];
			}
			elseif($tmp_query['operator'] == "like"){
				$tmp_queries[] = $tmp_query['field']." like '%".$tmp_query['value']."%'";
			}
			elseif($tmp_query['operator'] == "in"){
				if(!isset($in_value[$tmp_query['field']])){
					$in_value[$tmp_query['field']] = array();
				}
				$in_value[$tmp_query['field']][] = $tmp_query['value'];
			}
		}
		foreach($in_value as $field=>$in_value){
			$tmp_queries[] = $field. " IN (".implode(',',$in_value).")";
		}
		
		$tmp_queries = (sizeof($tmp_queries)>0)?implode(" AND ",$tmp_queries):"";

		if($tmp_queries != ""){
			if($query != ""){
				$query .= " AND ".$tmp_queries;		
			}		
			else{
				$query = $tmp_queries;					
			}
		}
		return $query;
	}
}