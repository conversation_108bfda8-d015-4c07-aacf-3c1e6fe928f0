<div class="row">
	<!-- Left Panel - Status Indicators -->
	<div class="col-md-6">
		<div class="panel panel-default">
			<div class="panel-body">
				<div role="form" class="form-horizontal ng-pristine ng-valid">
					<!-- CRA Number -->
					<div class="form-group">
						<label class="col-sm-4 control-label">CRA Number</label>
						<div class="col-sm-8">
							<input readonly class="form-control" value="<?php echo $item->id.'/CRA'; ?>">
						</div>
					</div>

					<!-- Status -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Status</label>
						<div class="col-sm-8">
							<input readonly class="form-control" value="<?php echo isset($item->status) ? $item->status : ''; ?>">
						</div>
					</div>

					<!-- Status Checkboxes (Auto-checked based on data) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Requesting Proof Document</label>
						<div class="col-sm-8">
							<div class="checkbox">
								<label class="ui-checks">
									<input type="checkbox" disabled <?php echo !empty($item->dateOfRequestingProofDocument) ? 'checked' : ''; ?>>
									<i></i>
								</label>
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-4 control-label">Proof Document Available</label>
						<div class="col-sm-8">
							<div class="checkbox">
								<label class="ui-checks">
									<input type="checkbox" disabled <?php echo !empty($item->dateOfReceivingProofDocument) ? 'checked' : ''; ?>>
									<i></i>
								</label>
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-4 control-label">STRAD Risk Assessment</label>
						<div class="col-sm-8">
							<div class="checkbox">
								<label class="ui-checks">
									<input type="checkbox" disabled <?php echo !empty($item->dateOfStradRiskAssessment) ? 'checked' : ''; ?>>
									<i></i>
								</label>
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-4 control-label">Approval</label>
						<div class="col-sm-8">
							<div class="checkbox">
								<label class="ui-checks">
									<input type="checkbox" disabled <?php echo !empty($item->dateOfCompleted) ? 'checked' : ''; ?>>
									<i></i>
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Right Panel - Editable Date Fields for SCI -->
	<div class="col-md-6">
		<div class="panel panel-default">
			<div class="panel-body">
				<div role="form" class="form-horizontal ng-pristine ng-valid">
					<!-- Date of Submitting (READ-ONLY) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Submitting</label>
						<div class="col-sm-8">
							<?php
								$dateOfSubmitting = "";
								if(isset($item) && isset($item->dateOfSubmitting)){
									$dateOfSubmitting = CraService::formatDate($item->dateOfSubmitting, 'Y-m-d');
								}
							?>
							<input readonly class="form-control" value="<?php echo $dateOfSubmitting; ?>">
						</div>
					</div>

					<!-- Date of Requesting Proof Document (EDITABLE for SCI) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Requesting Proof Document</label>
						<div class="col-sm-8">
							<?php
								$dateOfRequestingProofDocument = "";
								if(isset($item) && isset($item->dateOfRequestingProofDocument)){
									if (is_numeric($item->dateOfRequestingProofDocument)) {
										$dateOfRequestingProofDocument = date('Y-m-d', $item->dateOfRequestingProofDocument / 1000);
									} else {
										$dateOfRequestingProofDocument = $item->dateOfRequestingProofDocument;
									}
								}
							?>
							<div class="input-group">
								<input type="text" name="dateOfRequestingProofDocument" class="form-control date-picker" value="<?php echo $dateOfRequestingProofDocument;?>" data-date="<?php echo $dateOfRequestingProofDocument;?>" data-date-format="yyyy-mm-dd" id="dateOfRequestingProofDocument">
								<span class="input-group-btn">
									<button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
								</span>
							</div>
						</div>
					</div>

					<!-- Date of Receiving Proof Document (EDITABLE for SCI) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Receiving Proof Document</label>
						<div class="col-sm-8">
							<?php
								$dateOfReceivingProofDocument = "";
								if(isset($item) && isset($item->dateOfReceivingProofDocument)){
									if (is_numeric($item->dateOfReceivingProofDocument)) {
										$dateOfReceivingProofDocument = date('Y-m-d', $item->dateOfReceivingProofDocument / 1000);
									} else {
										$dateOfReceivingProofDocument = $item->dateOfReceivingProofDocument;
									}
								}
							?>
							<div class="input-group">
								<input type="text" name="dateOfReceivingProofDocument" class="form-control date-picker" value="<?php echo $dateOfReceivingProofDocument;?>" data-date="<?php echo $dateOfReceivingProofDocument;?>" data-date-format="yyyy-mm-dd" id="dateOfReceivingProofDocument">
								<span class="input-group-btn">
									<button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
								</span>
							</div>
						</div>
					</div>

					<!-- Date of STRAD Risk Assessment (READ-ONLY) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of STRAD Risk Assessment</label>
						<div class="col-sm-8">
							<?php
								$dateOfStradRiskAssessment = "";
								if(isset($item) && isset($item->dateOfStradRiskAssessment)){
									if (is_numeric($item->dateOfStradRiskAssessment)) {
										$dateOfStradRiskAssessment = date('Y-m-d', $item->dateOfStradRiskAssessment / 1000);
									} else {
										$dateOfStradRiskAssessment = $item->dateOfStradRiskAssessment;
									}
								}
							?>
							<input readonly class="form-control" value="<?php echo $dateOfStradRiskAssessment; ?>">
						</div>
					</div>

					<!-- Date of Approval (Table format) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Approval</label>
						<div class="col-sm-8">
							<?php if(isset($item->approvalData) && !empty($item->approvalData)): ?>
								<div class="table-responsive">
									<table class="table table-bordered table-striped">
										<thead>
											<tr>
												<th>Claim No</th>
												<th>Date of Approval</th>
											</tr>
										</thead>
										<tbody>
											<?php foreach($item->approvalData as $approval): ?>
											<tr>
												<td><?php echo $approval->claimNo; ?></td>
												<td><?php echo $approval->approvalDate; ?></td>
											</tr>
											<?php endforeach; ?>
										</tbody>
									</table>
								</div>
							<?php else: ?>
								<input readonly class="form-control" placeholder="No approvals yet">
							<?php endif; ?>
						</div>
					</div>

					<!-- Date of Completed (READ-ONLY) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Completed</label>
						<div class="col-sm-8">
							<?php
								$dateOfCompleted = "";
								if(isset($item) && isset($item->dateOfCompleted)){
									if (is_numeric($item->dateOfCompleted)) {
										$dateOfCompleted = date('Y-m-d', $item->dateOfCompleted / 1000);
									} else {
										$dateOfCompleted = $item->dateOfCompleted;
									}
								}
							?>
							<input readonly class="form-control" value="<?php echo $dateOfCompleted; ?>">
						</div>
					</div>

				<!-- STRAD Risk Assessment Files Section -->
				<div class="form-group">
					<label class="col-sm-4 control-label">STRAD Risk Assessment Files</label>
					<div class="col-sm-8">
						<input type="file" class="newStradRiskAssessmentFiles input-keyvisual inline-block" name="newStradRiskAssessmentFiles[]" multiple="multiple">
						<?php if(isset($item) && isset($item->stradRiskAssessmentFiles) && sizeof((array)$item->stradRiskAssessmentFiles)>0):?>
						<br>
						<div class="upload-file table-responsive">
							<table class="currentStradRiskAssessmentFiles table table-editable table-hover table-striped table-bordered no-margin">
								<thead class="text-nowrap">
									<tr>
										<th class="limit-width">Filename</th>
										<th>Tools</th>
									</tr>
								</thead>
								<tbody>
									<?php foreach($item->stradRiskAssessmentFiles as $file_id=>$file_name):?>
									<tr id="<?php echo $file_id;?>">
										<td class="limit-width">
											<?php echo $file_name;?>
										</td>
										<td class="text-center">
											<button class="btn btn-default no-border btn-sm download-file">
												<i class="fa fa-download"></i>
											</button>
											<button class="btn btn-default no-border btn-sm remove-file">
												<i class="fa fa-remove"></i>
											</button>
										</td>
									</tr>
									<?php endforeach;?>
								</tbody>
							</table>
						</div>
						<?php endif;?>
					</div>
				</div>

				<!-- Proof Documents Section -->
				<div class="form-group">
					<label class="col-sm-4 control-label">Proof Documents</label>
					<div class="col-sm-8">
						<input type="file" class="newProofDocuments input-keyvisual inline-block" name="newProofDocuments[]" multiple="multiple">
						<?php if(isset($item) && isset($item->proofDocuments) && sizeof((array)$item->proofDocuments)>0):?>
						<br>
						<div class="upload-file table-responsive">
							<table class="currentProofDocuments table table-editable table-hover table-striped table-bordered no-margin">
								<thead class="text-nowrap">
									<tr>
										<th class="limit-width">Filename</th>
										<th>Tools</th>
									</tr>
								</thead>
								<tbody>
									<?php foreach($item->proofDocuments as $file_id=>$file_name):?>
									<tr id="<?php echo $file_id;?>">
										<td class="limit-width">
											<?php echo $file_name;?>
										</td>
										<td class="text-center">
											<button class="btn btn-default no-border btn-sm download-file">
												<i class="fa fa-download"></i>
											</button>
											<button class="btn btn-default no-border btn-sm remove-file">
												<i class="fa fa-remove"></i>
											</button>
										</td>
									</tr>
									<?php endforeach;?>
								</tbody>
							</table>
						</div>
						<?php endif;?>
					</div>
				</div>

			</div>
			</div>
		</div>
	</div>
</div>
