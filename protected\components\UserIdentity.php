<?php

/**
 * UserIdentity represents the data needed to identity a user.
 * It contains the authentication method that checks if the provided
 * data can identity the user.
 */
class UserIdentity extends CUserIdentity
{
	private $_id;

	/**
	 * Authenticates a user.
	 * @return boolean whether authentication succeeds.
	 */
	public function authenticate()
	{
		$user=UserService::login(strtolower($this->username), $this->password);
		if(isset($user->id) && ($user->id != "")){
			$this->_id=$user->userId;
			$this->setState('email', $user->userEmail);
			$this->setState('fullName', $user->fullName);
			$this->setState('groupName', $user->groupName);
			$this->setState('groupId', $user->groupId);
			
			$group = GroupService::get($user->groupId);
			if($group->ascendantId > 0){
				$ascendant = GroupService::get($group->ascendantId);
				$this->setState('ascendantGroupName', $ascendant->name);
			}
			else{
				$this->setState('ascendantGroupName', "");
			}
			
			$this->setState('permissions', $user->permissions);
			$preference = UserService::getPreference($user->userId);
			$this->setState('preferenceId',$preference->id);
			$this->setState('filters',$preference->filters);
			if (isset($preference->labelFilters)){
			$this->setState('labelFilters',$preference->labelFilters);
			}
			if (isset($preference->advertisingFilters)){
            	$this->setState('advertisingFilters',$preference->advertisingFilters);
            }
			$this->setState('fields',$preference->selectedFields);
			$this->setState('labelFields',$preference->selectedLabelFields);
			$this->setState('advertisingFields',$preference->selectedAdvertisingFields);

			// Handle CRA fields migration from old separate fields to combined field
			$craFields = isset($preference->selectedCraFields) ? $preference->selectedCraFields : array();
			if (!empty($craFields)) {
				$migratedCraFields = array();
				$hasOldFields = false;
				foreach ($craFields as $field) {
					if ($field === 'expositionLevel' || $field === 'expositionDetail') {
						$hasOldFields = true;
						if (!in_array('exposition', $migratedCraFields)) {
							$migratedCraFields[] = 'exposition';
						}
					} else {
						$migratedCraFields[] = $field;
					}
				}

				// If migration occurred, update the preference
				if ($hasOldFields) {
					$preference->selectedCraFields = $migratedCraFields;
					try {
						PreferenceService::update($preference->id, $preference);
						$craFields = $migratedCraFields;
					} catch (Exception $e) {
						// If update fails, just use migrated fields for this session
						$craFields = $migratedCraFields;
					}
				}
			}
			$this->setState('craFields', $craFields);
			$this->errorCode=self::ERROR_NONE;
		}
		else{
			$this->errorCode=self::ERROR_USERNAME_INVALID;
		}
		return $this->errorCode==self::ERROR_NONE;
	}

	/**
	 * @return integer the ID of the user record
	 */
	public function getId()
	{
		return $this->_id;
	}
}