<?php
// Safety check: ensure $items is an array
if (!is_array($items)) {
    $items = array();
}

foreach($items as $item):
	// Skip null or invalid items
	if (!$item || !isset($item->id)) {
		continue;
	}
?>
<tr data-id="<?php echo $item->id;?>">
	<td class="text-center">
		<div class="checkbox no-margin">
			<label class="ui-checks">
				<input type="checkbox" value="<?php echo $item->id;?>" class="check-item">
				<i></i>
			</label>
		</div>
	</td>

	<!-- CRA Number (Database ID) -->
	<td class="column-id read-cra">
		<?php echo $item->id."/CRA"; ?>
	</td>

	<!-- Content Files -->
	<td class="column-contentFiles read-cra">
		<?php
		if (isset($item->contentFiles) && !empty($item->contentFiles)) {
			if (is_array($item->contentFiles)) {
				echo count($item->contentFiles) . ' files';
			} elseif (is_object($item->contentFiles)) {
				$count = count((array)$item->contentFiles);
				echo $count . ($count == 1 ? ' file' : ' files');
			} else {
				echo '1 file';
			}
		} else {
			echo '-';
		}
		?>
	</td>

	<!-- Date of Submitting -->
	<td class="column-dateOfSubmitting read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfSubmitting) ? $item->dateOfSubmitting : null, 'Y-m-d'); ?>
	</td>

	<!-- Date of Requesting Proof Document -->
	<td class="column-dateOfRequestingProofDocument read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfRequestingProofDocument) ? $item->dateOfRequestingProofDocument : null, 'Y-m-d'); ?>
	</td>

	<!-- Date of Receiving Proof Document -->
	<td class="column-dateOfReceivingProofDocument read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfReceivingProofDocument) ? $item->dateOfReceivingProofDocument : null, 'Y-m-d'); ?>
	</td>

	<!-- Proof Documents -->
	<td class="column-proofDocuments read-cra">
		<?php
		if (isset($item->proofDocuments) && !empty($item->proofDocuments)) {
			if (is_array($item->proofDocuments)) {
				echo count($item->proofDocuments) . ' documents';
			} elseif (is_object($item->proofDocuments)) {
				$count = count((array)$item->proofDocuments);
				echo $count . ($count == 1 ? ' document' : ' documents');
			} else {
				echo '1 document';
			}
		} else {
			echo '-';
		}
		?>
	</td>

	<!-- Date of STRAD Risk Assessment -->
	<td class="column-dateOfStradRiskAssessment read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfStradRiskAssessment) ? $item->dateOfStradRiskAssessment : null, 'Y-m-d'); ?>
	</td>

	<!-- Date of Completed -->
	<td class="column-dateOfCompleted read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfCompleted) ? $item->dateOfCompleted : null, 'Y-m-d'); ?>
	</td>

	<!-- Exposition (Combined Level and Detail) -->
	<td class="column-exposition read-cra w-md">
		<?php
		$expositionLevel = isset($item->expositionLevel) && !empty($item->expositionLevel) ? $item->expositionLevel : '';
		$expositionDetail = isset($item->expositionDetail) && !empty($item->expositionDetail) ? $item->expositionDetail : '';

		if (!empty($expositionLevel) && !empty($expositionDetail)) {
			echo $expositionLevel . ' - ' . $expositionDetail;
		} elseif (!empty($expositionLevel)) {
			echo $expositionLevel;
		} elseif (!empty($expositionDetail)) {
			echo $expositionDetail;
		} else {
			echo '-';
		}
		?>
	</td>

	<!-- Creator Email -->
	<td class="column-creatorEmail read-cra">
		<?php echo isset($item->creatorEmail) && !empty($item->creatorEmail) ? $item->creatorEmail : '-'; ?>
	</td>

	<!-- Follower Email -->
	<td class="column-followerEmail read-cra">
		<?php echo isset($item->followerEmail) && !empty($item->followerEmail) ? $item->followerEmail : '-'; ?>
	</td>

	<!-- Assignee Email -->
	<td class="column-assigneeEmail read-cra">
		<?php echo isset($item->assigneeEmail) && !empty($item->assigneeEmail) ? $item->assigneeEmail : '-'; ?>
	</td>

	<!-- Creator Full Name -->
	<td class="column-creatorFullName read-cra">
		<?php echo isset($item->creatorFullName) && !empty($item->creatorFullName) ? $item->creatorFullName : '-'; ?>
	</td>

	<!-- Follower Full Name -->
	<td class="column-followerFullName read-cra">
		<?php echo isset($item->followerFullName) && !empty($item->followerFullName) ? $item->followerFullName : '-'; ?>
	</td>

	<!-- Assignee Full Name -->
	<td class="column-assigneeFullName read-cra">
		<?php echo isset($item->assigneeFullName) && !empty($item->assigneeFullName) ? $item->assigneeFullName : '-'; ?>
	</td>

	<!-- Reference Files -->
	<td class="column-referencesFiles read-cra">
		<?php
		if (isset($item->referencesFiles) && !empty($item->referencesFiles)) {
			if (is_array($item->referencesFiles)) {
				echo count($item->referencesFiles) . ' files';
			} elseif (is_object($item->referencesFiles)) {
				$count = count((array)$item->referencesFiles);
				echo $count . ($count == 1 ? ' file' : ' files');
			} else {
				echo '1 file';
			}
		} else {
			echo '-';
		}
		?>
	</td>

	<!-- Requests -->
	<td class="column-requestIds read-cra">
		<?php
		if (isset($item->requestIds) && is_array($item->requestIds)) {
			echo implode(', ', $item->requestIds);
		} elseif (isset($item->requestIds) && !empty($item->requestIds)) {
			echo $item->requestIds;
		} else {
			echo '-';
		}
		?>
	</td>

	<!-- Status -->
	<td class="column-status read-cra">
		<?php echo isset($item->status) && !empty($item->status) ? $item->status : '-'; ?>
	</td>

	<!-- STRAD Risk Assessment Files -->
	<td class="column-stradRiskAssessmentFiles read-cra">
		<?php
		if (isset($item->stradRiskAssessmentFiles) && !empty($item->stradRiskAssessmentFiles)) {
			if (is_array($item->stradRiskAssessmentFiles)) {
				echo count($item->stradRiskAssessmentFiles) . ' files';
			} elseif (is_object($item->stradRiskAssessmentFiles)) {
				$count = count((array)$item->stradRiskAssessmentFiles);
				echo $count . ($count == 1 ? ' file' : ' files');
			} else {
				echo '1 file';
			}
		} else {
			echo '-';
		}
		?>
	</td>

	<!-- Timeline -->
	<td class="column-timeline read-cra">
		<?php echo CraService::formatDate(isset($item->timeline) ? $item->timeline : null, 'Y-m-d'); ?>
	</td>
</tr>
<?php
endforeach;
?>
