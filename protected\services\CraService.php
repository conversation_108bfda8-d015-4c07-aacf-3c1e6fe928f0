<?php
/**
 * Service class for CRA Request Management
 * Extends JService to inherit standard CRUD operations
 */
class CraService extends JService
{
    // API module name - must match backend endpoint
    public static $apiModule = "cra-requests";
    
    // Workflow status filters for UI
    public static $filters = array(
        'WAIT_FOR_MKT_SUBMISSION' => 'Wait for MKT submission',
        'WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN' => 'Wait for STRAD Director to assign',
        'WAIT_FOR_STRAD_RISK_ASSESSMENT' => 'Wait for STRAD risk assessment',
        'WAIT_FOR_APPROVAL' => 'Wait for approval',
        'COMPLETED' => 'Completed',
        'CANCEL' => 'Cancelled'
    );
    
    // Field definitions for dynamic display
    public static $list_all_fields = array(
        'id' => 'CRA Request No',
        'status' => 'Status',
        'exposition' => 'Exposition',
        'requestIds' => 'Request IDs',
        'assigneeEmail' => 'Email of Assignee',
        'followerEmail' => 'Email of Follower',
        'creatorEmail' => 'Email of Creator',
        'assigneeFullName' => 'Fullname of assignee',
        'followerFullName' => 'Fullname of follower',
        'creatorFullName' => 'Fullname of creator',
        'contentFiles' => 'Content Files',
        'referencesFiles' => 'Reference Files',
        'timeline' => 'Timeline',
        'dateOfSubmitting' => 'Date of Submitting',
        'dateOfRequestingProofDocument' => 'Date of Requesting Proof Document',
        'dateOfReceivingProofDocument' => 'Date of Receiving Proof Document',
        'dateOfStradRiskAssessment' => 'Date of STRAD Risk Assessment',
        'dateOfCompleted' => 'Date of Completed',
        'proofDocuments' => 'Proof Documents',
        'stradRiskAssessmentFiles' => 'STRAD Risk Assessment Files'
    );

    // Export fields for Excel export functionality
    public static $list_export_fields = array(
        'id' => 'CRA Request No',
        'contentFiles' => 'Content Files',
        'dateOfSubmitting' => 'Date of Submitting',
        'dateOfRequestingProofDocument' => 'Date of Requesting Proof Document',
        'dateOfReceivingProofDocument' => 'Date of Receiving Proof Document',
        'dateOfStradRiskAssessment' => 'Date of STRAD Risk Assessment',
        'dateOfCompleted' => 'Date of Completed',
        'exposition' => 'Exposition',
        'creatorEmail' => 'Email of Creator',
        'followerEmail' => 'Email of Follower',
        'assigneeEmail' => 'Email of Assignee',
        'creatorFullName' => 'Fullname of creator',
        'followerFullName' => 'Fullname of follower',
        'assigneeFullName' => 'Fullname of assignee',
        'proofDocuments' => 'Proof Documents',
        'referencesFiles' => 'Reference Files',
        'requests' => 'Requests',
        'status' => 'Status',
        'stradRiskAssessmentFiles' => 'STRAD Risk Assessment Files',
        'timeline' => 'Timeline'
    );

    // Exposition level options
    public static $expositionLevels = array(
        'Low exposition' => 'Low exposition',
        'Medium exposition' => 'Medium exposition',
        'High exposition' => 'High exposition'
    );
    
    // Exposition detail options (cascading)
    public static $expositionDetails = array(
        'Low exposition' => array(
            'Point of sales' => 'Point of sales',
            'Press' => 'Press'
        ),
        'Medium exposition' => array(
            'Back of the packaging, some social network, digital retailer' => 'Back of the packaging, some social network, digital retailer',
            'L\'Oreal brand website, including a retailing part' => 'L\'Oreal brand website, including a retailing part'
        ),
        'High exposition' => array(
            'Facing of the packing, TVC, QVC (shopping channel)' => 'Facing of the packing, TVC, QVC (shopping channel)'
        )
    );
    
    // Advertisement type options
    public static $advertisementTypes = array(
        'KV' => 'KV',
        'Clip' => 'Clip'
    );
    
    /**
     * Get exposition details for a specific level
     */
    public static function getExpositionDetailsForLevel($level) {
        if (isset(self::$expositionDetails[$level])) {
            return self::$expositionDetails[$level];
        }
        return array();
    }
    
    /**
     * Workflow transition methods
     */
    public static function submit($id) {
        return self::post($id . '/submit');
    }
    
    public static function assign($id, $assigneeId) {
        if (!isset(Yii::app()->session['token'])) {
            throw new CHttpException(403, 'You are not authorized to perform this action.');
        }

        $url = self::$baseUrl . "/" . self::$apiModule . "/" . $id . "/assign";
        $data = array('assigneeId' => $assigneeId);

        // Debug logging
        Yii::log("CRA Assign API call - URL: {$url}", CLogger::LEVEL_INFO);
        Yii::log("CRA Assign API call - Data: " . print_r($data, true), CLogger::LEVEL_INFO);
        Yii::log("CRA Assign API call - Form data: " . http_build_query($data), CLogger::LEVEL_INFO);

        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Authorization: Bearer " . Yii::app()->session['token']
        ));

        curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));

        // Make the REST call, returning the result
        $response = curl_exec($curl);
        if ($response === FALSE) {
            throw new CHttpException(500, 'Connection Failure.');
        }

        $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($http_status == 200) {
            $result = json_decode($response);
            return $result;
        } elseif ($http_status == 401) {
            throw new CHttpException(403, 'You are not authorized to perform this action.');
        } else {
            $result = json_decode($response);
            $errorMessage = 'API request failed';
            if ($result && isset($result->errorMessage)) {
                $errorMessage = $result->errorMessage;
            } elseif ($result && isset($result->message)) {
                $errorMessage = $result->message;
            }
            throw new CHttpException($http_status, $errorMessage);
        }
    }
    
    public static function assess($id, $assessmentData) {
        return self::post($id . '/assess', $assessmentData);
    }
    
    public static function approve($id) {
        return self::post($id . '/approve');
    }

    public static function cancel($id, $reason) {
        return self::post($id . '/cancel', array('reason' => $reason));
    }

    public static function saveRiskAssessment($id) {
        return self::post($id . '/save-risk-assessment');
    }
    
    /**
     * Override create method to properly format data for CRA API
     */
    public static function create($data) {
        if (!isset(Yii::app()->session['token'])) {
            throw new CHttpException(403, 'You are not authorized to perform this action.');
        }

        // Format data according to API specification
        $apiData = self::formatDataForApi($data);

        $curl = curl_init(self::$baseUrl . "/" . self::$apiModule);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($apiData));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer " . Yii::app()->session['token']
        ));

        $response = curl_exec($curl);
        $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($httpStatus == 200 || $httpStatus == 201) {
            $result = json_decode($response);
            if ($result && isset($result->id)) {
                // Process the response to format dates and other fields
                return self::processApiResponse($result);
            }
            throw new Exception('Invalid response from API');
        } else {
            $result = json_decode($response);
            $errorMessage = 'API request failed';
            if ($result && isset($result->errorMessage)) {
                $errorMessage = $result->errorMessage;
            } elseif ($result && isset($result->message)) {
                $errorMessage = $result->message;
            }
            throw new CHttpException($httpStatus, $errorMessage);
        }
    }

    /**
     * Format form data for API submission
     */
    private static function formatDataForApi($data) {
        $apiData = array();

        // Include ID field for update operations
        if (isset($data['id']) && !empty($data['id'])) {
            $apiData['id'] = intval($data['id']);
        }

        // Convert requestIds to array of integers
        if (isset($data['requestIds']) && !empty($data['requestIds'])) {
            if (is_string($data['requestIds'])) {
                // Split comma-separated string and convert to integers
                $requestIds = array_map('trim', explode(',', $data['requestIds']));
                $apiData['requestIds'] = array_map('intval', array_filter($requestIds, 'is_numeric'));
            } elseif (is_array($data['requestIds'])) {
                $apiData['requestIds'] = array_map('intval', $data['requestIds']);
            }
        }

        // Required exposition fields
        if (isset($data['expositionLevel'])) {
            $apiData['expositionLevel'] = $data['expositionLevel'];
        }
        if (isset($data['expositionDetail'])) {
            $apiData['expositionDetail'] = $data['expositionDetail'];
        }

        // Advertisement type
        if (isset($data['advertisementType'])) {
            $apiData['advertisementType'] = $data['advertisementType'];
        }

        // Timeline - convert date string to timestamp if needed
        if (isset($data['timeline']) && !empty($data['timeline'])) {
            if (is_string($data['timeline'])) {
                // Convert date string to timestamp (milliseconds)
                $timestamp = strtotime($data['timeline']);
                if ($timestamp !== false) {
                    $apiData['timeline'] = $timestamp * 1000; // Convert to milliseconds
                }
            } else {
                $apiData['timeline'] = $data['timeline'];
            }
        }

        // File fields - these should be handled by file upload process
        if (isset($data['contentFiles']) && (is_array($data['contentFiles']) || is_object($data['contentFiles']))) {
            $apiData['contentFiles'] = $data['contentFiles'];
        }
        if (isset($data['referencesFiles']) && (is_array($data['referencesFiles']) || is_object($data['referencesFiles']))) {
            $apiData['referencesFiles'] = $data['referencesFiles'];
        }
        if (isset($data['proofDocuments']) && (is_array($data['proofDocuments']) || is_object($data['proofDocuments']))) {
            $apiData['proofDocuments'] = $data['proofDocuments'];
        }

        // Status field
        if (isset($data['status']) && !empty($data['status'])) {
            $apiData['status'] = $data['status'];
        }

        // Other optional fields
        $optionalFields = array('messages');
        foreach ($optionalFields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                $apiData[$field] = $data[$field];
            }
        }

        return $apiData;
    }

    /**
     * Process API response to format dates and other fields for display
     */
    private static function processApiResponse($response) {
        if (!$response) {
            return $response;
        }

        // Convert timestamp fields back to readable dates
        $timestampFields = array('created', 'updated', 'timeline');
        foreach ($timestampFields as $field) {
            if (isset($response->$field) && is_numeric($response->$field)) {
                // Convert milliseconds to seconds and format
                $timestamp = $response->$field / 1000;
                $response->{$field . '_formatted'} = date('Y-m-d H:i:s', $timestamp);
                if ($field === 'timeline') {
                    $response->{$field . '_date'} = date('Y-m-d', $timestamp);
                }
            }
        }

        return $response;
    }

    /**
     * Helper method for POST requests
     */
    private static function post($endpoint, $data = array()) {
        if (!isset(Yii::app()->session['token'])) {
            throw new CHttpException(403, 'You are not authorized to perform this action.');
        }

        $curl = curl_init(self::$baseUrl . "/" . self::$apiModule . "/" . $endpoint);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer " . Yii::app()->session['token']
        ));

        $response = curl_exec($curl);
        $httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($httpStatus == 200) {
            $result = json_decode($response);
            return self::processApiResponse($result);
        } else {
            $result = json_decode($response);
            $errorMessage = 'API request failed';
            if ($result && isset($result->errorMessage)) {
                $errorMessage = $result->errorMessage;
            } elseif ($result && isset($result->message)) {
                $errorMessage = $result->message;
            }
            throw new CHttpException($httpStatus, $errorMessage);
        }
    }

    /**
     * Override count method to handle CRA-specific filters
     * Converts filter IDs to status queries instead of sending filter parameter to backend
     */
    public static function count($queries=array(), $query="", $filter=null) {
        // Convert filter to status query if provided
        if ($filter !== null && isset(self::$filters[$filter])) {
            // Add status condition to query
            $statusQuery = "status = '" . $filter . "'";
            if (!empty($query)) {
                $query = $query . " AND " . $statusQuery;
            } else {
                $query = $statusQuery;
            }
        }

        // Call parent method without filter parameter
        return parent::count($queries, $query, null);
    }

    /**
     * Override get method to process response data
     */
    public static function get($id) {
        $result = parent::get($id);
        if ($result) {
            $result = self::processApiResponse($result);
        }
        return $result;
    }

    /**
     * Override getList method to handle CRA-specific filters
     * Converts filter IDs to status queries instead of sending filter parameter to backend
     */
    public static function getList($page=1, $orders=array(), $queries=array(), $query="", $filter=null) {
        // Convert filter to status query if provided
        if ($filter !== null && isset(self::$filters[$filter])) {
            // Add status condition to query
            $statusQuery = "status = '" . $filter . "'";
            if (!empty($query)) {
                $query = $query . " AND " . $statusQuery;
            } else {
                $query = $statusQuery;
            }
        }

        // Call parent method without filter parameter
        $result = parent::getList($page, $orders, $queries, $query, null);

        // Process each item in the result list
        if ($result && isset($result->data) && is_array($result->data)) {
            foreach ($result->data as $index => $item) {
                $result->data[$index] = self::processApiResponse($item);
            }
        }

        return $result;
    }

    /**
     * Safely format date value - handles both timestamp and string dates
     */
    public static function formatDate($dateValue, $format = 'Y-m-d H:i') {
        if (!$dateValue) {
            return '-';
        }

        // If it's numeric (timestamp), divide by 1000 to convert from milliseconds
        if (is_numeric($dateValue)) {
            return date($format, $dateValue / 1000);
        }

        // If it's a string, use strtotime to parse it
        $timestamp = strtotime($dateValue);
        if ($timestamp === false) {
            return $dateValue; // Return original value if can't parse
        }

        return date($format, $timestamp);
    }

    /**
     * Override updateFull method to properly format data for CRA API
     */
    public static function updateFull($id, $item) {
        if (!isset(Yii::app()->session['token'])) {
            throw new CHttpException(403, 'You are not authorized to perform this action.');
        }

        // If $item is an object, convert it to array for processing
        if (is_object($item)) {
            $item = (array) $item;
        }

        // Format form data according to API specification
        $formData = self::formatDataForApi($item);

        // Preserve metadata fields that should not be lost during update
        // These are fields that are set by the system and should not be overridden by form data
        $preserveFields = array(
            'created', 'updated', 'creatorId', 'creatorEmail', 'creatorFullName',
            'assigneeId', 'assigneeEmail', 'assigneeFullName',
            'followerId', 'followerEmail', 'followerFullName',
            'dateOfSubmitting', 'dateOfRequestingProofDocument', 'dateOfReceivingProofDocument',
            'dateOfStradRiskAssessment', 'dateOfCompleted', 'stradRiskAssessmentFiles',
            'proofDocuments', 'id'
        );

        // Start with formatted form data
        $apiData = $formData;

        // Preserve system-managed metadata fields
        foreach ($preserveFields as $field) {
            if (isset($item[$field])) {
                $apiData[$field] = $item[$field];
            }
        }

        // Handle status field specially - it can be updated by form but should preserve existing value if not in form
        // formatDataForApi() already handles status if it's in the form data
        if (!isset($apiData['status']) && isset($item['status'])) {
            $apiData['status'] = $item['status'];
        }

        // Note: We don't send formatted fields to API as they are generated on response
        // The API expects raw timestamp values, not formatted strings

        // Debug logging - log final API data
        Yii::log("CRA Update - Final API data being sent: " . json_encode($apiData), CLogger::LEVEL_INFO, 'cra.update.debug');

        $curl = curl_init(self::$baseUrl . "/" . self::$apiModule . "/" . $id);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json;charset=utf-8',
            "Authorization: Bearer " . Yii::app()->session['token']
        ));
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($apiData));

        // Make the REST call, returning the result
        $response = curl_exec($curl);
        if ($response === FALSE) {
            throw new CHttpException(500, 'Connection Failure.');
        }

        $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if ($http_status == 200) {
            return true;
        } elseif ($http_status == 401) {
            throw new CHttpException(403, 'You are not authorized to perform this action.');
        } else {
            throw new CHttpException($http_status, 'API Error: ' . $response);
        }
    }

    public static function xls($fields, $orders = array(), $queries = array(), $query = "", $filter = null) {
        $url = self::$baseUrl . '/cra-requests/xls';
        $data = array(
            'fields' => $fields,
            'orders' => $orders,
            'queries' => $queries,
            'query' => $query
        );
        if ($filter !== null) {
            $data['filter'] = $filter;
        }
        $curl = curl_init($url . "?" . http_build_query($data));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Authorization: Bearer " . Yii::app()->session['token']
        ));

        $response = curl_exec($curl);
        if ($response === FALSE) {
            throw new CHttpException(500, 'Connection Failure.');
        }
        $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        if ($http_status == 200) {
            return $response;
        } else {
            throw new CHttpException(500, 'Export failed with status: ' . $http_status);
        }
    }
}
