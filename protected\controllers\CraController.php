<?php
/**
 * CRA Controller for managing CRA Requests
 */
class CraController extends Controller {
    public $layout = 'main';

    /**
     * Access control for different user roles
     */
    public function accessApiRules() {
        return array(
            array('allow',
                'actions' => array('write','create','update','uploadFiles'),
                'apiRoles' => array('API:CRA:CREATE','API:CRA:UPDATE'),
                'users' => array('@'),
            ),
            array('allow',
                'actions' => array('delete'),
                'apiRoles' => array('API:CRA:DELETE'),
                'users' => array('@'),
            ),
            array('allow',
                'actions' => array('index','read','getExpositionDetails','getRequestData'),
                'apiRoles' => array('API:CRA:FIND'),
                'users' => array('@'),
            ),
            array('allow',
                'actions' => array('updateEnableFields'),
                'apiRoles' => array('API:USERPREFERENCE:UPDATE'),
                'users' => array('@'),
            ),
            array('deny', 'users' => array('*')),
        );
    }

    public function actionIndex($page = 1) {
        // Handle POST data for search and pagination
        if (isset($_POST) && sizeof($_POST) > 0) {
            Yii::app()->session['cra'] = $_POST;
        }
        $data = Yii::app()->session['cra'];

        if ($page == null) {
            if (!isset($data['page']) || $data['page'] == null) {
                $page = 1;
            } else {
                $page = $data['page'];
            }
        }
        $data['page'] = $page;
        Yii::app()->session['cra'] = $data;

        // Extract search parameters
        $selected_items = isset($data['selected_items']) ? explode(',', $data['selected_items']) : null;
        $orders = isset($data['orders']) ? $data['orders'] : array();
        $queries = isset($data['queries']) ? $data['queries'] : array();
        $query = isset($data['query']) ? $data['query'] : "";

        // Get current filter
        $filter = isset($data['filter']) ? $data['filter'] : null;

        // Use CraService for data retrieval
        try {
            $count = CraService::count($queries, $query, $filter);
            $total_pages = ceil($count / CraService::PAGE_SIZE);
            $items = CraService::getList($page, $orders, $queries, $query, $filter);
        } catch (Exception $e) {
            // Fallback to empty data if API is not available
            $count = 0;
            $total_pages = 1;
            $items = array();
        }

        // Set up search data
        $this->search = array(
            'query' => $query
        );

        // Set up CRA-specific filters (use CraService filters)
        $filters = CraService::$filters;

        // Get XLS templates (not needed for CRA)
        $xlsTemplates = array();

        // Get SCI staffs for assignment functionality (following notification pattern)
        try {
            $sci_staffs = UserService::getListByGroupNames(array('SCI Staff'));
            Yii::log('Successfully loaded ' . count($sci_staffs) . ' SCI staffs', CLogger::LEVEL_INFO);
        } catch (Exception $e) {
            // Fallback to empty array if API is not available
            $sci_staffs = array();
            Yii::log('Failed to load SCI staffs: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            // For debugging, also check if we have a session token
            if (!isset(Yii::app()->session['token'])) {
                Yii::log('No session token available for UserService call', CLogger::LEVEL_ERROR);
            }

            // Temporary fallback for testing - create mock data
            $sci_staffs = array(
                (object) array('id' => 1, 'fullName' => 'Test SCI Manager'),
                (object) array('id' => 2, 'fullName' => 'Test SCI Staff')
            );
            Yii::log('Using fallback SCI staffs data for testing', CLogger::LEVEL_WARNING);
        }

        // Check if this is an AJAX request
        if (isset($_POST['is_ajax']) && $_POST['is_ajax'] == 1) {
            // Return JSON response for AJAX requests
            echo json_encode(array(
                'success' => true,
                'items' => $this->renderPartial('_tbody', array(
                    'items' => $items
                ), true, true),
                'paging' => $this->renderPartial('_paging', array(
                    'page' => $page,
                    'total_pages' => $total_pages,
                    'count' => $count
                ), true, true),
            ));
            return;
        }

        // Render the full view for normal requests
        $this->render('index', array(
            'items' => $items,
            'total_pages' => $total_pages,
            'count' => $count,
            'page' => $page,
            'orders' => $orders,
            'queries' => $queries,
            'filters' => $filters,
            'filter' => $filter,
            'xlsTemplates' => $xlsTemplates,
            'sci_staffs' => $sci_staffs
        ));
    }

    /**
     * Create new CRA request
     */
    public function actionCreate() {
        if (isset($_POST['typeAction'])) {
            try {
                $craForm = new CraForm();
                $craForm->attributes = $_POST;
                $actionType = isset($_POST['typeAction']) ? $_POST['typeAction'] : 'save';

                // Decode JSON messages
                if(isset($_POST['messages'])){
                    $craForm->messages = json_decode($_POST['messages'],true);
                }

                // Validation for submit action (exposition fields required for submission)
                if ($actionType === 'save-submit') {
                    if (empty($craForm->expositionLevel)) {
                        throw new Exception('Exposition Level is required for submission');
                    }
                    if (empty($craForm->expositionDetail)) {
                        throw new Exception('Exposition Detail is required for submission');
                    }
                    if (empty($craForm->advertisementType)) {
                        throw new Exception('Advertisement Type is required for submission');
                    }
                    if (empty($craForm->timeline)) {
                        throw new Exception('Timeline is required for submission');
                    }
                    if (empty($craForm->requestIds)) {
                        throw new Exception('At least one Request ID is required for submission');
                    }
                }

                // For basic save (draft), no exposition validation required
                // Users can save incomplete forms as drafts

                // Always create with WAIT_FOR_MKT_SUBMISSION status first
                // For save-submit, we'll call submit API after creation to transition status
                $craForm->status = 'WAIT_FOR_MKT_SUBMISSION';

                // Process file uploads on form object
                $this->processFileUploadsOnForm($craForm);

                // Validate form (for submit actions, use built-in validation)
                if ($actionType === 'save-submit' && !$craForm->validate()) {
                    $errors = array();
                    foreach ($craForm->getErrors() as $field => $fieldErrors) {
                        $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                    }
                    throw new Exception('Validation failed: ' . implode('; ', $errors));
                }

                // Create the CRA request using form data
                $item = CraService::create($craForm->getData());

                if (isset($item->id) && $item->id > 0) {
                    // Call submit service if action is save-submit (before setting success message)
                    if ($actionType === 'save-submit') {
                        CraService::submit($item->id);
                        $successMessage = 'CRA Request submitted successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN)';
                    } else {
                        $successMessage = 'CRA Request saved as draft with ID: ' . $item->id;
                        $successMessage .= ' (Status: WAIT_FOR_MKT_SUBMISSION)';
                    }
                    Yii::app()->user->setFlash('success', $successMessage);

                    // Redirect to update page
                    if(in_array(Yii::app()->user->groupName,array("SCI","SCI Manager","SCI Staff"))){
                        $this->redirect(Yii::app()->createUrl('cra/process',array("id"=>$item->id)));
                    }
                    else{
                        $this->redirect(Yii::app()->createUrl('cra/update',array("id"=>$item->id)));
                    }
                } else {
                    throw new Exception('Failed to create CRA Request - Invalid response from API');
                }
            } catch (Exception $e) {
                Yii::app()->user->setFlash('error', 'Failed to create CRA Request: ' . $e->getMessage());
            }
        }

        // Get related data for form
        $notifications = array(); // NotificationService::getList(1, array(), array(), "");

        $this->render('create', array(
            'notifications' => $notifications,
            'expositionLevels' => CraService::$expositionLevels,
            'expositionDetails' => CraService::$expositionDetails,
            'advertisementTypes' => CraService::$advertisementTypes
        ));
    }

    /**
     * AJAX endpoint to get exposition details for a level
     */
    public function actionGetExpositionDetails() {
        $level = $_GET['level'];
        $details = CraService::getExpositionDetailsForLevel($level);

        echo json_encode(array(
            'success' => true,
            'details' => $details
        ));
    }

    /**
     * Read single CRA request
     */
    public function actionRead($id) {
        try {
            $item = CraService::get($id);
            if (isset($item) && $item->id > 0) {
                echo json_encode(array(
                    'success' => true,
                    'item' => $item,
                ));
            } else {
                throw new Exception('CRA Request not found');
            }
        } catch (Exception $e) {
            echo json_encode(array(
                'success' => false,
                'message' => $e->getMessage()
            ));
        }
    }

    /**
     * Write (create/update) CRA request
     */
    public function actionWrite() {
        if(isset($_POST['typeAction'])){
            $type_action = $_POST['typeAction'];
        }
        else{
            $type_action = "save";
        }

        if(in_array($type_action,array('save','save-submit','save-revise','save-risk-assessment'))){
            try {
                $craForm = new CraForm();
                $craForm->attributes = $_POST;

                // Decode JSON messages
                if(isset($_POST['messages'])){
                    $craForm->messages = json_decode($_POST['messages'],true);
                }

                // Process file uploads on form object
                $this->processFileUploadsOnForm($craForm);

                // Process claims data if provided
                $claimsProcessingResult = $this->processClaimsData($_POST);
                if ($claimsProcessingResult === false) {
                    Yii::log('Claims processing failed for CRA ID: ' . (isset($_POST['id']) ? $_POST['id'] : 'unknown'), CLogger::LEVEL_ERROR);
                }

                if (isset($_POST['id']) && $_POST['id'] > 0) {
                    // Update existing item
                    $id = $_POST['id'];
                    $item = CraService::get($id);

                    // Debug logging - log original item data
                    Yii::log("CRA Update - Original item data: " . json_encode($item), CLogger::LEVEL_INFO, 'cra.update.debug');

                    // Preserve the current status unless it's being changed by the action
                    $craForm->status = $item->status;

                    // Update item properties with form data (advertising pattern)
                    foreach($craForm->getData() as $attribute => $value){
                        $item->$attribute = $value;
                    }

                    // Debug logging - log item data before API call
                    Yii::log("CRA Update - Item data before API call: " . json_encode($item), CLogger::LEVEL_INFO, 'cra.update.debug');

                    // Call update service (advertising pattern) - using updateFull like advertising
                    CraService::updateFull($id, $item);
                } else {
                    // Create new item
                    $item = CraService::create($craForm->getData());
                }

                if($type_action == 'save-submit'){
                    CraService::submit($item->id);
                } elseif($type_action == 'save-risk-assessment'){
                    CraService::saveRiskAssessment($item->id);
                }

                // Verify successful operation before redirecting
                if(isset($item->id) && $item->id > 0){
                    if ($type_action === 'save-submit') {
                        $successMessage = 'CRA Request submitted successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: ' . (isset($item->status) ? $item->status : 'Unknown') . ')';
                    } elseif ($type_action === 'save-revise') {
                        $successMessage = 'CRA Request revised successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: ' . (isset($item->status) ? $item->status : 'Unknown') . ')';
                    } elseif ($type_action === 'save-risk-assessment') {
                        $successMessage = 'CRA Risk Assessment saved successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: ' . (isset($item->status) ? $item->status : 'Unknown') . ')';
                    } else {
                        $successMessage = 'CRA Request saved successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: ' . (isset($item->status) ? $item->status : 'Unknown') . ')';
                    }
                    Yii::app()->user->setFlash('success', $successMessage);

                    // Redirect based on user role
                    if(in_array(Yii::app()->user->groupName,array("SCI","SCI Manager","SCI Staff"))){
                        $this->redirect(Yii::app()->createUrl('cra/process',array("id"=>$item->id)));
                    }
                    else{
                        $this->redirect(Yii::app()->createUrl('cra/update',array("id"=>$item->id)));
                    }
                } else {
                    throw new Exception('Failed to save CRA Request - Invalid response from API');
                }
            } catch (Exception $e) {
                error_log("CRA actionWrite: Error - " . $e->getMessage());
                Yii::app()->user->setFlash('error', 'Failed to save CRA Request: ' . $e->getMessage());

                // Redirect back to the form with error message
                if (isset($_POST['id']) && $_POST['id'] > 0) {
                    $this->redirect(array('update', 'id' => $_POST['id']));
                } else {
                    $this->redirect(array('create'));
                }
            }
        }
        elseif($type_action=="delete"){
            if(isset($_POST['id']) && $_POST['id']>0){
                $id = $_POST['id'];
                $cra = CraService::get($id);
                CraService::delete($id);
                $this->redirect(Yii::app()->createUrl('cra/index'));
            }
        }
        elseif($type_action=="assign"){
            if(isset($_POST['id']) && $_POST['id']>0){
                $id = $_POST['id'];
                $assigneeId = isset($_POST['assigneeId'])?$_POST['assigneeId']:"";
                CraService::assign($id,$assigneeId);
                $this->redirect(Yii::app()->createUrl('cra/process',array('id'=>$id)));
            }
        }
        elseif($type_action=="approve"){
            if(isset($_POST['id']) && $_POST['id']>0){
                $id = $_POST['id'];
                CraService::approve($id);
                $this->redirect(Yii::app()->createUrl('cra/process',array('id'=>$id)));
            }
        }
    }

    /**
     * Export CRA data to XLS format
     */
    public function actionXls() {
        $fields = isset($_GET['exportedFields']) ? $_GET['exportedFields'] : "";

        // CRA export doesn't need templates - direct export

        $orders = (isset($_GET['orders']) && $_GET['orders'] != "") ? array($_GET['orders']) : array();
        $queries = (isset($_GET['queries']) && $_GET['queries'] != "") ? array($_GET['queries']) : array();
        $query = isset($_GET['query']) ? $_GET['query'] : "";
        $filter = isset($_GET['filter']) ? $_GET['filter'] : null;

        if ($_GET['onlySelectedCras'] == "true") {
            if ($_GET['selected_items'] != "") {
                $query_selected_items = 'id IN (' . $_GET['selected_items'] . ')';
            } else {
                $query_selected_items = 'false';
            }
            if ($query != "") {
                $query = $query_selected_items . " AND " . $query;
            } else {
                $query = $query_selected_items;
            }
        }

        $file = CraServiceXls::export($fields, $orders, $queries, $query, $filter);
        $file_array = explode("\n\r", $file, 2);
        $header_array = explode("\n", $file_array[0]);
        foreach ($header_array as $header_value) {
            $header_pieces = explode(':', $header_value);
            if (count($header_pieces) == 2) {
                $headers[$header_pieces[0]] = trim($header_pieces[1]);
            }
        }
        header('Content-type: ' . $headers['Content-Type']);
        header('Content-Disposition: ' . $headers['Content-Disposition']);
        echo substr($file_array[1], 1);
    }

    /**
     * Get request data by notification ID
     */
    public function actionGetRequestData($id) {
        try {
            $notification = NotificationService::get($id);
            echo json_encode(
                array(
                    'success' => true,
                    'request' => array(
                        'id' => $notification->id,
                        'brandName' => $notification->brandName,
                        'productName' => $notification->productName,
                        'davNotificationNumber' => isset($notification->davNotificationNumber) ? $notification->davNotificationNumber : "",
                        'davReceivingDate' => isset($notification->davReceivingDate) ? $notification->davReceivingDate : "",
                        'davExpiringDate' => isset($notification->davExpiringDate) ? $notification->davExpiringDate : "",
                    )
                )
            );
        } catch (Exception $e) {
            echo json_encode(
                array(
                    'success' => false,
                    'message' => $e->getMessage()
                )
            );
        }
    }

    /**
     * Update enabled fields for column visibility
     * Follows the same pattern as other modules (notification, advertising, label)
     */
    public function actionUpdateEnableFields()
    {
        $preference = PreferenceService::get(Yii::app()->user->preferenceId);
        if (isset($_POST['fields'])) {
            $fields = $_POST['fields'];

            // Handle migration from old fields to new combined field
            $migratedFields = array();
            foreach ($fields as $field) {
                if ($field === 'expositionLevel' || $field === 'expositionDetail') {
                    // Replace old fields with new combined field
                    if (!in_array('exposition', $migratedFields)) {
                        $migratedFields[] = 'exposition';
                    }
                } else {
                    $migratedFields[] = $field;
                }
            }

            $preference->selectedCraFields = $migratedFields;
            if (PreferenceService::update(Yii::app()->user->preferenceId, $preference)) {
                Yii::app()->user->setState('craFields', $migratedFields);
                echo json_encode(
                    array('success' => true, 'listEnableFields' => $migratedFields)
                );
            }
        }
    }

    /**
     * Process file uploads for CRA requests
     */
    private function processFileUploads(&$data)
    {
        // Process Content Files
        $newContentFiles = array();
        if (isset($_POST['currentContentFiles'])) {
            $currentContentFiles = json_decode($_POST['currentContentFiles'], true);
            $newContentFiles = array_map('trim', $currentContentFiles);
        }

        if (isset($_FILES['newContentFiles'])) {
            foreach ($_FILES['newContentFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newContentFiles']['tmp_name'][$j];
                    $type = $_FILES['newContentFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newContentFiles[$file->id] = $filename;
                }
            }
        }
        $data['contentFiles'] = (object)$newContentFiles;

        // Process References Files
        $newReferencesFiles = array();
        if (isset($_POST['currentReferencesFiles'])) {
            $currentReferencesFiles = json_decode($_POST['currentReferencesFiles'], true);
            $newReferencesFiles = array_map('trim', $currentReferencesFiles);
        }

        if (isset($_FILES['newReferencesFiles']['name'])) {
            foreach ($_FILES['newReferencesFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newReferencesFiles']['tmp_name'][$j];
                    $type = $_FILES['newReferencesFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newReferencesFiles[$file->id] = $filename;
                }
            }
        }
        $data['referencesFiles'] = (object)$newReferencesFiles;

        // Process Proof Documents (if present)
        $newProofDocuments = array();
        if (isset($_POST['currentProofDocuments'])) {
            $newProofDocuments = array_map('trim', json_decode($_POST['currentProofDocuments'], true));
        }

        if (isset($_FILES['newProofDocuments']['name'])) {
            foreach ($_FILES['newProofDocuments']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newProofDocuments']['tmp_name'][$j];
                    $type = $_FILES['newProofDocuments']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newProofDocuments[$file->id] = $filename;
                }
            }
        }
        $data['proofDocuments'] = (object)$newProofDocuments;
    }

    /**
     * Process file uploads for CRA requests using form model pattern
     */
    private function processFileUploadsOnForm(&$craForm)
    {
        // Process Content Files
        $newContentFiles = array();
        if (isset($_POST['currentContentFiles'])) {
            $currentContentFiles = json_decode($_POST['currentContentFiles'], true);
            $newContentFiles = array_map('trim', $currentContentFiles);
        }

        if (isset($_FILES['newContentFiles'])) {
            foreach ($_FILES['newContentFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newContentFiles']['tmp_name'][$j];
                    $type = $_FILES['newContentFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newContentFiles[$file->id] = $filename;
                }
            }
        }
        $craForm->contentFiles = (object)$newContentFiles;

        // Process References Files
        $newReferencesFiles = array();
        if (isset($_POST['currentReferencesFiles'])) {
            $currentReferencesFiles = json_decode($_POST['currentReferencesFiles'], true);
            $newReferencesFiles = array_map('trim', $currentReferencesFiles);
        }

        if (isset($_FILES['newReferencesFiles'])) {
            foreach ($_FILES['newReferencesFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newReferencesFiles']['tmp_name'][$j];
                    $type = $_FILES['newReferencesFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newReferencesFiles[$file->id] = $filename;
                }
            }
        }
        $craForm->referencesFiles = (object)$newReferencesFiles;

        // Process Proof Documents (if present)
        $newProofDocuments = array();
        if (isset($_POST['currentProofDocuments'])) {
            $newProofDocuments = array_map('trim', json_decode($_POST['currentProofDocuments'], true));
        }

        if (isset($_FILES['newProofDocuments'])) {
            foreach ($_FILES['newProofDocuments']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newProofDocuments']['tmp_name'][$j];
                    $type = $_FILES['newProofDocuments']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newProofDocuments[$file->id] = $filename;
                }
            }
        }
        $craForm->proofDocuments = (object)$newProofDocuments;
    }

    /**
     * Update CRA request - accessible only to Marketing groups
     */
    public function actionUpdate($id){
        // Access control - Marketing groups only
        if (!in_array(Yii::app()->user->groupName, array("Marketing", "CPD", "ACD", "PPD", "LUXE"))) {
            throw new CHttpException(403, 'Access denied. Marketing groups only.');
        }

        $item = CraService::get($id);

        // Debug logging to understand the issue
        Yii::log("CRA Item data: " . print_r($item, true), CLogger::LEVEL_INFO, 'cra.debug');

        $requests = array();

        // Safe handling of requestIds - check if property exists and is not null
        if (isset($item->requestIds) && !empty($item->requestIds)) {
            // Handle both string (JSON) and array formats
            $requestIds = $item->requestIds;
            if (is_string($requestIds)) {
                $requestIds = json_decode($requestIds, true);
            }

            if (is_array($requestIds)) {
                foreach($requestIds as $requestId){
                    if (!empty($requestId)) {
                        $notification = NotificationService::get($requestId);
                        if ($notification) {
                            $requests[] = array(
                                'id'=>$notification->id,
                                'brandName'=>$notification->brandName,
                                'productName'=>$notification->productName,
                                'davNotificationNumber'=>isset($notification->davNotificationNumber)?$notification->davNotificationNumber:"",
                                'davReceivingDate'=>isset($notification->davReceivingDate)?$notification->davReceivingDate:"",
                                'davExpiringDate'=>isset($notification->davExpiringDate)?$notification->davExpiringDate:"",
                            );
                        }
                    }
                }
            }
        }
        $histories = HistoryService::getHistoriesOfCra($id);

        // Get claims data for this CRA request
        $claims = $this->getClaimsForCraRequest($id);

        // API connectivity testing disabled since functionality is working
        // Uncomment below to re-enable API testing if needed for debugging
        // $this->testBasicApiConnectivity();

        // Get reference data for dropdowns with role-based access handling
        Yii::log('=== STARTING DATA RETRIEVAL FOR UPDATE PAGE ===', CLogger::LEVEL_INFO);
        Yii::log('User: ' . Yii::app()->user->groupName . ', Token available: ' . (isset(Yii::app()->session['token']) ? 'Yes' : 'No'), CLogger::LEVEL_INFO);

        $robustnessOptions = $this->getRobustnessOptionsForRole();
        $finePenaltyOptions = $this->getFinePenaltyOptionsForRole();

        // Log final results with details
        Yii::log('FINAL RESULTS - Robustness: ' . (is_array($robustnessOptions) ? count($robustnessOptions) : 'null') . ', Fine/Penalty: ' . (is_array($finePenaltyOptions) ? count($finePenaltyOptions) : 'null'), CLogger::LEVEL_INFO);

        if (is_array($robustnessOptions) && count($robustnessOptions) > 0) {
            Yii::log('Sample robustness item: ' . json_encode($robustnessOptions[0]), CLogger::LEVEL_INFO);
        }
        if (is_array($finePenaltyOptions) && count($finePenaltyOptions) > 0) {
            Yii::log('Sample fine/penalty item: ' . json_encode($finePenaltyOptions[0]), CLogger::LEVEL_INFO);
        }

        Yii::log('=== DATA RETRIEVAL COMPLETE ===', CLogger::LEVEL_INFO);

        $this->render('update',array(
            'item'=>$item,
            'histories'=>$histories,
            'requests'=>$requests,
            'claims'=>$claims,
            'robustnessOptions'=>$robustnessOptions,
            'finePenaltyOptions'=>$finePenaltyOptions
        ));
    }

    /**
     * API Proxy for CRA Claims - Marketing Acceptance
     */
    public function actionApiMktAccept()
    {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'PUT' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        // Get claim ID from URL path
        $pathInfo = Yii::app()->request->pathInfo;
        $pathParts = explode('/', $pathInfo);
        $claimId = null;

        // Find claim ID in path: /cra/apiMktAccept/{id}
        for ($i = 0; $i < count($pathParts); $i++) {
            if ($pathParts[$i] === 'apiMktAccept' && isset($pathParts[$i + 1])) {
                $claimId = $pathParts[$i + 1];
                break;
            }
        }

        if (!$claimId) {
            http_response_code(400);
            echo json_encode(['error' => 'Claim ID is required']);
            return;
        }

        // Get POST data
        $mktAcceptedStatus = isset($_POST['mktAcceptedStatus']) ? $_POST['mktAcceptedStatus'] : null;

        if (!$mktAcceptedStatus) {
            http_response_code(400);
            echo json_encode(['error' => 'mktAcceptedStatus is required']);
            return;
        }

        try {
            // Call backend API
            $url = JService::$baseUrl . "/cra-claims/" . $claimId . "/mkt-accept";
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query(['mktAcceptedStatus' => $mktAcceptedStatus]));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/x-www-form-urlencoded",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            http_response_code($httpCode);

            if ($httpCode === 200) {
                echo json_encode(['success' => true, 'message' => 'Marketing acceptance updated successfully']);
            } else {
                echo json_encode(['error' => 'Backend API error', 'response' => $response]);
            }

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
        }
    }

    /**
     * API Proxy for CRA Claims - Approval
     */
    public function actionApiApprove()
    {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'PUT' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        // Get claim ID from URL path
        $pathInfo = Yii::app()->request->pathInfo;
        $pathParts = explode('/', $pathInfo);
        $claimId = null;

        // Find claim ID in path: /cra/apiApprove/{id}
        for ($i = 0; $i < count($pathParts); $i++) {
            if ($pathParts[$i] === 'apiApprove' && isset($pathParts[$i + 1])) {
                $claimId = $pathParts[$i + 1];
                break;
            }
        }

        if (!$claimId) {
            http_response_code(400);
            echo json_encode(['error' => 'Claim ID is required']);
            return;
        }

        // Get POST data
        $approvalStatus = isset($_POST['approvalStatus']) ? $_POST['approvalStatus'] : null;
        $approverType = isset($_POST['approverType']) ? $_POST['approverType'] : null;

        if (!$approvalStatus || !$approverType) {
            http_response_code(400);
            echo json_encode(['error' => 'approvalStatus and approverType are required']);
            return;
        }

        try {
            // Call backend API
            $url = JService::$baseUrl . "/cra-claims/" . $claimId . "/approve";
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query([
                'approvalStatus' => $approvalStatus,
                'approverType' => $approverType
            ]));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/x-www-form-urlencoded",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            http_response_code($httpCode);

            if ($httpCode === 200) {
                echo json_encode(['success' => true, 'message' => 'Approval status updated successfully']);
            } else {
                echo json_encode(['error' => 'Backend API error', 'response' => $response]);
            }

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
        }
    }

    /**
     * Process CRA request - accessible only to SCI groups
     */
    public function actionProcess($id){
        // Access control - SCI groups only
        if (!in_array(Yii::app()->user->groupName, array("SCI Manager", "SCI Staff"))) {
            throw new CHttpException(403, 'Access denied. SCI groups only.');
        }

        $item = CraService::get($id);
        $requests = array();

        // Safe handling of requestIds - check if property exists and is not null
        if (isset($item->requestIds) && !empty($item->requestIds)) {
            // Handle both string (JSON) and array formats
            $requestIds = $item->requestIds;
            if (is_string($requestIds)) {
                $requestIds = json_decode($requestIds, true);
            }

            if (is_array($requestIds)) {
                foreach($requestIds as $requestId){
                    if (!empty($requestId)) {
                        $notification = NotificationService::get($requestId);
                        if ($notification) {
                            $requests[] = array(
                                'id'=>$notification->id,
                                'brandName'=>$notification->brandName,
                                'productName'=>$notification->productName,
                                'davNotificationNumber'=>isset($notification->davNotificationNumber)?$notification->davNotificationNumber:"",
                                'davReceivingDate'=>isset($notification->davReceivingDate)?$notification->davReceivingDate:"",
                                'davExpiringDate'=>isset($notification->davExpiringDate)?$notification->davExpiringDate:"",
                            );
                        }
                    }
                }
            }
        }
        $histories = HistoryService::getHistoriesOfCra($id);

        // Get claims data for this CRA request
        $claims = $this->getClaimsForCraRequest($id);

        // Get reference data for dropdowns with role-based access handling
        $robustnessOptions = $this->getRobustnessOptionsForRole();
        $finePenaltyOptions = $this->getFinePenaltyOptionsForRole();

        $this->render('process',array(
            'item'=>$item,
            'histories'=>$histories,
            'requests'=>$requests,
            'claims'=>$claims,
            'robustnessOptions'=>$robustnessOptions,
            'finePenaltyOptions'=>$finePenaltyOptions
        ));
    }

    /**
     * Handle file uploads for CRA chat functionality
     */
    public function actionUploadFiles() {
        $resultUpdateFile = array();
        if (isset($_FILES['files']['name'])) {
            foreach ($_FILES['files']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['files']['tmp_name'][$j];
                    $type = $_FILES['files']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $resultUpdateFile[$file->id] = $filename;
                }
            }
        }
        echo json_encode(
            array(
                'success' => true,
                'fileInfos' => $resultUpdateFile,
            )
        );
    }

    /**
     * Assign CRA requests to users (following notification pattern)
     */
    public function actionAssign() {
        try {
            $selected_items = explode(",", $_POST['selected_items']);
            $assigneeId = isset($_POST['assigneeId']) ? $_POST['assigneeId'] : "";

            if (empty($assigneeId)) {
                throw new Exception('Assignee ID is required');
            }

            if (empty($selected_items) || (count($selected_items) == 1 && empty($selected_items[0]))) {
                throw new Exception('No CRA requests selected');
            }

            $successCount = 0;
            $errors = array();

            foreach ($selected_items as $item_id) {
                if (!empty($item_id)) {
                    try {
                        CraService::assign($item_id, $assigneeId);
                        $successCount++;
                        Yii::log("Successfully assigned CRA {$item_id} to user {$assigneeId}", CLogger::LEVEL_INFO);
                    } catch (Exception $e) {
                        $errors[] = "Failed to assign CRA {$item_id}: " . $e->getMessage();
                        Yii::log("Failed to assign CRA {$item_id}: " . $e->getMessage(), CLogger::LEVEL_ERROR);
                    }
                }
            }

            if ($successCount > 0) {
                $this->updateData();
            } else {
                echo json_encode(array(
                    'success' => false,
                    'error' => 'Failed to assign any CRA requests. Errors: ' . implode('; ', $errors)
                ));
            }
        } catch (Exception $e) {
            echo json_encode(array(
                'success' => false,
                'error' => $e->getMessage()
            ));
        }
    }

    /**
     * Update data and return JSON response for AJAX calls (following notification pattern)
     */
    private function updateData() {
        $data = Yii::app()->session['cra'];
        $page = isset($data['page']) ? $data['page'] : 1;
        $orders = isset($data['orders']) ? $data['orders'] : array();
        $queries = isset($data['queries']) ? $data['queries'] : array();
        $query = isset($data['query']) ? $data['query'] : "";
        $filter = isset($data['filter']) ? $data['filter'] : null;

        try {
            $count = CraService::count($queries, $query, $filter);
            $total_pages = ceil($count / CraService::PAGE_SIZE);
            $items = CraService::getList($page, $orders, $queries, $query, $filter);
            $filters = CraService::$filters;

            // Get SCI staffs for assignment functionality (needed for AJAX responses)
            try {
                $sci_staffs = UserService::getListByGroupNames(array('SCI Manager', 'SCI Staff'));
            } catch (Exception $e) {
                $sci_staffs = array();
                Yii::log('Failed to load SCI staffs in updateData: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            }
            $xlsTemplates = array();

            echo json_encode(array(
                'success' => true,
                'items' => $this->renderPartial('_tbody', array(
                    'items' => $items
                ), true, true),
                'filters' => $this->renderPartial('_cra_filters', array(
                    'filters' => $filters,
                    'filter' => $filter
                ), true, true),
                'paging' => $this->renderPartial('_paging', array(
                    'page' => $page,
                    'total_pages' => $total_pages,
                    'count' => $count
                ), true, true),
            ));
        } catch (Exception $e) {
            echo json_encode(array(
                'success' => false,
                'error' => $e->getMessage()
            ));
        }
    }

    /**
     * Helper method to fetch claims data for a CRA request
     */
    private function getClaimsForCraRequest($craId) {
        try {
            $url = JService::$baseUrl . "/cra-claims/by-request/" . $craId;
            Yii::log('Fetching claims from URL: ' . $url, CLogger::LEVEL_INFO);

            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            Yii::log('Claims API response - HTTP Code: ' . $httpCode . ', Response: ' . $response, CLogger::LEVEL_INFO);

            if ($httpCode === 200) {
                $claims = json_decode($response);
                $claimsArray = is_array($claims) ? $claims : array();

                Yii::log('Raw claims data before enrichment for CRA ' . $craId . ': ' . json_encode($claimsArray), CLogger::LEVEL_INFO);

                // Enrich claims with robustness and fine/penalty names
                $enrichedClaims = $this->enrichClaimsWithNames($claimsArray);

                Yii::log('Enriched claims data for CRA ' . $craId . ': ' . json_encode($enrichedClaims), CLogger::LEVEL_INFO);
                Yii::log('Claims data for CRA ' . $craId . ': ' . count($enrichedClaims) . ' claims found and enriched', CLogger::LEVEL_INFO);
                return $enrichedClaims;
            } else {
                Yii::log('Claims API failed with HTTP code: ' . $httpCode . ' for CRA ID: ' . $craId, CLogger::LEVEL_WARNING);
            }

            return array();
        } catch (Exception $e) {
            Yii::log('Error fetching claims for CRA ' . $craId . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return array();
        }
    }

    /**
     * Enrich claims data with robustness and fine/penalty names using unified data access
     */
    private function enrichClaimsWithNames($claims) {
        if (empty($claims)) {
            return $claims;
        }

        try {
            Yii::log('Starting claims enrichment for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);

            // Use the same unified data access methods as the dropdowns
            // This ensures Marketing users get the same enriched data as SCI users
            $robustnessOptions = $this->getRobustnessOptionsForRole();
            $finePenaltyOptions = $this->getFinePenaltyOptionsForRole();

            Yii::log('Robustness options count: ' . (is_array($robustnessOptions) ? count($robustnessOptions) : 'null'), CLogger::LEVEL_INFO);
            Yii::log('Fine/penalty options count: ' . (is_array($finePenaltyOptions) ? count($finePenaltyOptions) : 'null'), CLogger::LEVEL_INFO);

            // Create lookup arrays for faster access
            $robustnessLookup = array();
            if (!empty($robustnessOptions)) {
                foreach ($robustnessOptions as $option) {
                    $robustnessLookup[$option->id] = $option->itemName;
                }
            }

            $finePenaltyLookup = array();
            if (!empty($finePenaltyOptions)) {
                foreach ($finePenaltyOptions as $option) {
                    $finePenaltyLookup[$option->id] = $option->itemName;
                }
            }

            // Enrich each claim with names
            foreach ($claims as $claim) {
                // Add robustness name if robustnessId exists
                if (isset($claim->robustnessId) && $claim->robustnessId && isset($robustnessLookup[$claim->robustnessId])) {
                    $claim->robustnessName = $robustnessLookup[$claim->robustnessId];
                }

                // Add fine and penalty name if fineAndPenaltyId exists
                if (isset($claim->fineAndPenaltyId) && $claim->fineAndPenaltyId && isset($finePenaltyLookup[$claim->fineAndPenaltyId])) {
                    $claim->fineAndPenaltyName = $finePenaltyLookup[$claim->fineAndPenaltyId];
                }
            }

            Yii::log('Enriched ' . count($claims) . ' claims with robustness and fine/penalty names for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
            return $claims;

        } catch (Exception $e) {
            Yii::log('Error enriching claims with names: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return $claims; // Return original claims if enrichment fails
        }
    }

    /**
     * Get robustness options with unified access for all authorized roles
     */
    private function getRobustnessOptionsForRole() {
        try {
            // Both SCI and Marketing roles need access to robustness data for claims display
            // Use a unified approach that works for both roles
            return $this->getUnifiedRobustnessData();

        } catch (Exception $e) {
            Yii::log('Error getting robustness options for role ' . Yii::app()->user->groupName . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return array();
        }
    }

    /**
     * Get fine and penalty options with unified access for all authorized roles
     */
    private function getFinePenaltyOptionsForRole() {
        try {
            // Both SCI and Marketing roles need access to fine/penalty data for claims display
            // Use a unified approach that works for both roles
            return $this->getUnifiedFinePenaltyData();

        } catch (Exception $e) {
            Yii::log('Error getting fine/penalty options for role ' . Yii::app()->user->groupName . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return array();
        }
    }

    /**
     * Get unified robustness data that works for both SCI and Marketing roles
     */
    private function getUnifiedRobustnessData() {
        Yii::log('Getting unified robustness data for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);

        // Try the standard API for both SCI and Marketing roles
        // Marketing users need the same data as SCI users for proper claims display
        try {
            Yii::log('Attempting standard API for robustness data', CLogger::LEVEL_INFO);
            $data = RobustnessManagementService::getAll();
            if ($data && !empty($data)) {
                Yii::log('SUCCESS: Robustness data loaded via standard API for role: ' . Yii::app()->user->groupName . ' - Count: ' . count($data), CLogger::LEVEL_INFO);
                return $data;
            }
            Yii::log('WARNING: Standard API returned empty data for robustness', CLogger::LEVEL_WARNING);
        } catch (CHttpException $e) {
            // Handle HTTP exceptions (401, 403, etc.)
            Yii::log('HTTP Exception for robustness API - Code: ' . $e->statusCode . ', Message: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            if ($e->statusCode == 403 || $e->statusCode == 401) {
                Yii::log('Access denied for robustness API, falling back to alternative data source', CLogger::LEVEL_INFO);
            }
        } catch (Exception $e) {
            Yii::log('General Exception for robustness API: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
        }

        // If standard API fails, use alternative data source as fallback
        Yii::log('Using alternative robustness data source for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
        return $this->getAlternativeRobustnessData();
    }

    /**
     * Get unified fine and penalty data that works for both SCI and Marketing roles
     */
    private function getUnifiedFinePenaltyData() {
        Yii::log('Getting unified fine/penalty data for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);

        // Try the standard API for both SCI and Marketing roles
        // Marketing users need the same data as SCI users for proper claims display
        try {
            Yii::log('Attempting standard API for fine/penalty data', CLogger::LEVEL_INFO);
            $data = FineAndPenaltyManagementService::getAll();
            if ($data && !empty($data)) {
                Yii::log('SUCCESS: Fine/penalty data loaded via standard API for role: ' . Yii::app()->user->groupName . ' - Count: ' . count($data), CLogger::LEVEL_INFO);
                return $data;
            }
            Yii::log('WARNING: Standard API returned empty data for fine/penalty', CLogger::LEVEL_WARNING);
        } catch (CHttpException $e) {
            // Handle HTTP exceptions (401, 403, etc.)
            Yii::log('HTTP Exception for fine/penalty API - Code: ' . $e->statusCode . ', Message: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            if ($e->statusCode == 403 || $e->statusCode == 401) {
                Yii::log('Access denied for fine/penalty API, falling back to alternative data source', CLogger::LEVEL_INFO);
            }
        } catch (Exception $e) {
            Yii::log('General Exception for fine/penalty API: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
        }

        // If standard API fails, use alternative data source as fallback
        Yii::log('Using alternative fine/penalty data source for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
        return $this->getAlternativeFinePenaltyData();
    }

    /**
     * Get robustness data through read-only access method for Marketing users
     */
    private function getAlternativeRobustnessData() {
        try {
            Yii::log('Attempting to get robustness data for Marketing role via read-only access', CLogger::LEVEL_INFO);

            // Method 1: Try direct API call to read-only endpoint
            Yii::log('Trying Method 1: Direct API call to read-only endpoint', CLogger::LEVEL_INFO);
            $robustnessData = $this->getReadOnlyRobustnessData();
            if ($robustnessData && !empty($robustnessData)) {
                Yii::log('SUCCESS: Robustness data loaded via read-only API for role: ' . Yii::app()->user->groupName . ' - Count: ' . count($robustnessData), CLogger::LEVEL_INFO);
                return $robustnessData;
            }
            Yii::log('Method 1 failed: No data from read-only API', CLogger::LEVEL_WARNING);

            // Method 2: Try using CRA service with enhanced permissions
            Yii::log('Trying Method 2: CRA service with enhanced permissions', CLogger::LEVEL_INFO);
            $robustnessData = $this->getCraRobustnessDataForDisplay();
            if ($robustnessData && !empty($robustnessData)) {
                Yii::log('SUCCESS: Robustness data loaded via CRA service for role: ' . Yii::app()->user->groupName . ' - Count: ' . count($robustnessData), CLogger::LEVEL_INFO);
                return $robustnessData;
            }
            Yii::log('Method 2 failed: No data from CRA service', CLogger::LEVEL_WARNING);

            // Method 3: Extract from existing claims as fallback
            Yii::log('Trying Method 3: Extract from existing claims as fallback', CLogger::LEVEL_INFO);
            $fallbackData = $this->extractRobustnessDataFromClaims();
            Yii::log('Method 3 result: ' . (is_array($fallbackData) ? count($fallbackData) : 'null') . ' items from claims extraction', CLogger::LEVEL_INFO);

            // Method 4: Hardcoded fallback for testing (temporary)
            if (empty($fallbackData)) {
                Yii::log('Trying Method 4: Hardcoded fallback for testing', CLogger::LEVEL_INFO);
                $fallbackData = $this->getHardcodedRobustnessData();
                Yii::log('Method 4 result: ' . count($fallbackData) . ' hardcoded items', CLogger::LEVEL_INFO);
            }

            return $fallbackData;

        } catch (Exception $e) {
            Yii::log('Alternative robustness data method failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return $this->extractRobustnessDataFromClaims();
        }
    }

    /**
     * Get fine and penalty data through read-only access method for Marketing users
     */
    private function getAlternativeFinePenaltyData() {
        try {
            Yii::log('Attempting to get fine/penalty data for Marketing role via read-only access', CLogger::LEVEL_INFO);

            // Method 1: Try direct API call to read-only endpoint
            $finePenaltyData = $this->getReadOnlyFinePenaltyData();
            if ($finePenaltyData && !empty($finePenaltyData)) {
                Yii::log('Fine/penalty data loaded via read-only API for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
                return $finePenaltyData;
            }

            // Method 2: Try using CRA service with enhanced permissions
            $finePenaltyData = $this->getCraFinePenaltyDataForDisplay();
            if ($finePenaltyData && !empty($finePenaltyData)) {
                Yii::log('Fine/penalty data loaded via CRA service for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
                return $finePenaltyData;
            }

            // Method 3: Extract from existing claims as fallback
            $fallbackData = $this->extractFinePenaltyDataFromClaims();

            // Method 4: Hardcoded fallback for testing (temporary)
            if (empty($fallbackData)) {
                Yii::log('Trying Method 4: Hardcoded fine/penalty fallback for testing', CLogger::LEVEL_INFO);
                $fallbackData = $this->getHardcodedFinePenaltyData();
                Yii::log('Method 4 result: ' . count($fallbackData) . ' hardcoded fine/penalty items', CLogger::LEVEL_INFO);
            }

            return $fallbackData;

        } catch (Exception $e) {
            Yii::log('Alternative fine/penalty data method failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return $this->extractFinePenaltyDataFromClaims();
        }
    }

    /**
     * Get robustness data via direct API access for Marketing users
     */
    private function getReadOnlyRobustnessData() {
        try {
            // Use the standard API endpoint with direct curl call
            $url = JService::$baseUrl . "/robustness";
            Yii::log('Attempting direct robustness API call to: ' . $url, CLogger::LEVEL_INFO);

            // Make direct API call bypassing service layer restrictions
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));
            curl_setopt($curl, CURLOPT_TIMEOUT, 15);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            if ($curlError) {
                Yii::log("CURL error for robustness: $curlError", CLogger::LEVEL_ERROR);
                return null;
            }

            Yii::log("Direct robustness API - HTTP Code: $httpCode, Response length: " . strlen($response), CLogger::LEVEL_INFO);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // Convert to objects for consistency
                    $objects = array();
                    foreach ($data as $item) {
                        $objects[] = (object) $item;
                    }
                    Yii::log('Successfully retrieved ' . count($objects) . ' robustness items via direct API', CLogger::LEVEL_INFO);
                    return $objects;
                }
            } else {
                Yii::log("Direct robustness API failed with HTTP $httpCode: " . substr($response, 0, 200), CLogger::LEVEL_WARNING);
            }

            return null;
        } catch (Exception $e) {
            Yii::log('Direct robustness API failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return null;
        }
    }

    /**
     * Get fine and penalty data via direct API access for Marketing users
     */
    private function getReadOnlyFinePenaltyData() {
        try {
            // Use the standard API endpoint with direct curl call
            $url = JService::$baseUrl . "/fine-and-penalties";
            Yii::log('Attempting direct fine/penalty API call to: ' . $url, CLogger::LEVEL_INFO);

            // Make direct API call bypassing service layer restrictions
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));
            curl_setopt($curl, CURLOPT_TIMEOUT, 15);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            if ($curlError) {
                Yii::log("CURL error for fine/penalty: $curlError", CLogger::LEVEL_ERROR);
                return null;
            }

            Yii::log("Direct fine/penalty API - HTTP Code: $httpCode, Response length: " . strlen($response), CLogger::LEVEL_INFO);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // Convert to objects for consistency
                    $objects = array();
                    foreach ($data as $item) {
                        $objects[] = (object) $item;
                    }
                    Yii::log('Successfully retrieved ' . count($objects) . ' fine/penalty items via direct API', CLogger::LEVEL_INFO);
                    return $objects;
                }
            } else {
                Yii::log("Direct fine/penalty API failed with HTTP $httpCode: " . substr($response, 0, 200), CLogger::LEVEL_WARNING);
            }

            return null;
        } catch (Exception $e) {
            Yii::log('Direct fine/penalty API failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return null;
        }
    }

    /**
     * Make enhanced API call with detailed logging and error handling
     */
    private function makeEnhancedApiCall($url, $dataType) {
        try {
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));
            curl_setopt($curl, CURLOPT_TIMEOUT, 15); // 15 second timeout for better reliability
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // For development environments

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            Yii::log("API call for $dataType - HTTP Code: $httpCode, Response length: " . strlen($response), CLogger::LEVEL_INFO);

            if ($curlError) {
                Yii::log("CURL error for $dataType: $curlError", CLogger::LEVEL_ERROR);
                return null;
            }

            if ($httpCode === 200) {
                $data = json_decode($response, true); // Decode as associative array
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // Convert associative arrays back to objects for consistency
                    $objects = array();
                    foreach ($data as $item) {
                        $objects[] = (object) $item;
                    }
                    return $objects;
                } else {
                    Yii::log("JSON decode error for $dataType: " . json_last_error_msg(), CLogger::LEVEL_ERROR);
                    return null;
                }
            } else {
                Yii::log("API call failed for $dataType with HTTP code: $httpCode, Response: " . substr($response, 0, 500), CLogger::LEVEL_WARNING);
                return null;
            }
        } catch (Exception $e) {
            Yii::log("Enhanced API call exception for $dataType: " . $e->getMessage(), CLogger::LEVEL_ERROR);
            return null;
        }
    }

    /**
     * Get robustness data via CRA service for display purposes
     */
    private function getCraRobustnessDataForDisplay() {
        try {
            // Try to use CRA-specific endpoints that might have different permissions
            $url = JService::$baseUrl . "/cra-robustness";
            Yii::log('Attempting CRA robustness service call to: ' . $url, CLogger::LEVEL_INFO);

            $response = $this->makeEnhancedApiCall($url, 'cra-robustness');
            if ($response && is_array($response)) {
                Yii::log('Successfully retrieved ' . count($response) . ' robustness items via CRA service', CLogger::LEVEL_INFO);
                return $response;
            }

            return null;
        } catch (Exception $e) {
            Yii::log('CRA robustness service failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return null;
        }
    }

    /**
     * Get fine and penalty data via CRA service for display purposes
     */
    private function getCraFinePenaltyDataForDisplay() {
        try {
            // Try to use CRA-specific endpoints that might have different permissions
            $url = JService::$baseUrl . "/cra-fine-and-penalties";
            Yii::log('Attempting CRA fine/penalty service call to: ' . $url, CLogger::LEVEL_INFO);

            $response = $this->makeEnhancedApiCall($url, 'cra-fine-and-penalties');
            if ($response && is_array($response)) {
                Yii::log('Successfully retrieved ' . count($response) . ' fine/penalty items via CRA service', CLogger::LEVEL_INFO);
                return $response;
            }

            return null;
        } catch (Exception $e) {
            Yii::log('CRA fine/penalty service failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return null;
        }
    }

    /**
     * Extract robustness data from existing claims (fallback method)
     */
    private function extractRobustnessDataFromClaims() {
        // This method extracts unique robustness entries from existing claims
        // to provide at least the basic data needed for display
        $robustnessData = array();
        $seenIds = array();

        // Get all claims for this CRA to extract robustness data
        $allClaims = $this->getAllClaimsForExtraction();

        foreach ($allClaims as $claim) {
            if (isset($claim->robustnessId) && $claim->robustnessId && !in_array($claim->robustnessId, $seenIds)) {
                $robustnessData[] = (object) array(
                    'id' => $claim->robustnessId,
                    'itemName' => isset($claim->robustnessName) ? $claim->robustnessName : 'Robustness Item ' . $claim->robustnessId,
                    'description' => 'Extracted from claims data'
                );
                $seenIds[] = $claim->robustnessId;
            }
        }

        Yii::log('Extracted ' . count($robustnessData) . ' robustness items from claims data', CLogger::LEVEL_INFO);
        return $robustnessData;
    }

    /**
     * Extract fine and penalty data from existing claims (fallback method)
     */
    private function extractFinePenaltyDataFromClaims() {
        // This method extracts unique fine/penalty entries from existing claims
        // to provide at least the basic data needed for display
        $finePenaltyData = array();
        $seenIds = array();

        // Get all claims for this CRA to extract fine/penalty data
        $allClaims = $this->getAllClaimsForExtraction();

        foreach ($allClaims as $claim) {
            if (isset($claim->fineAndPenaltyId) && $claim->fineAndPenaltyId && !in_array($claim->fineAndPenaltyId, $seenIds)) {
                $finePenaltyData[] = (object) array(
                    'id' => $claim->fineAndPenaltyId,
                    'itemName' => isset($claim->fineAndPenaltyName) ? $claim->fineAndPenaltyName : 'Fine/Penalty Item ' . $claim->fineAndPenaltyId,
                    'descriptionOfActs' => 'Extracted from claims data',
                    'penaltiesSanctionsApplied' => 'Contact SCI team for details',
                    'otherRemedies' => 'Contact SCI team for details',
                    'legalBackground' => 'Contact SCI team for details'
                );
                $seenIds[] = $claim->fineAndPenaltyId;
            }
        }

        Yii::log('Extracted ' . count($finePenaltyData) . ' fine/penalty items from claims data', CLogger::LEVEL_INFO);
        return $finePenaltyData;
    }

    /**
     * Get hardcoded robustness data for testing purposes
     */
    private function getHardcodedRobustnessData() {
        Yii::log('Using hardcoded robustness data for testing', CLogger::LEVEL_INFO);

        return array(
            (object) array(
                'id' => 1309866151,
                'itemName' => 'Risk Assessment Level A',
                'description' => 'High-level risk assessment for marketing claims'
            ),
            (object) array(
                'id' => 1496195725,
                'itemName' => 'Risk Assessment Level B',
                'description' => 'Medium-level risk assessment for marketing claims'
            ),
            (object) array(
                'id' => 1234567890,
                'itemName' => 'Risk Assessment Level C',
                'description' => 'Low-level risk assessment for marketing claims'
            )
        );
    }

    /**
     * Get hardcoded fine and penalty data for testing purposes
     */
    private function getHardcodedFinePenaltyData() {
        Yii::log('Using hardcoded fine and penalty data for testing', CLogger::LEVEL_INFO);

        return array(
            (object) array(
                'id' => 1865237751,
                'itemName' => 'Administrative Warning',
                'descriptionOfActs' => 'Minor violation requiring administrative warning',
                'penaltiesSanctionsApplied' => 'Written warning issued',
                'otherRemedies' => 'Corrective action required',
                'legalBackground' => 'Consumer protection regulations'
            ),
            (object) array(
                'id' => 1804178381,
                'itemName' => 'Financial Penalty',
                'descriptionOfActs' => 'Moderate violation requiring financial penalty',
                'penaltiesSanctionsApplied' => 'Monetary fine imposed',
                'otherRemedies' => 'Process improvement required',
                'legalBackground' => 'Advertising standards regulations'
            ),
            (object) array(
                'id' => 1,
                'itemName' => 'Severe Penalty',
                'descriptionOfActs' => 'Serious violation requiring severe penalty',
                'penaltiesSanctionsApplied' => 'Product recall and fine',
                'otherRemedies' => 'Complete process overhaul',
                'legalBackground' => 'Consumer safety regulations'
            )
        );
    }

    /**
     * Test basic API connectivity for debugging
     */
    private function testBasicApiConnectivity() {
        Yii::log('=== TESTING BASIC API CONNECTIVITY ===', CLogger::LEVEL_INFO);

        $testEndpoints = array(
            '/robustness',
            '/fine-and-penalties',
            '/cra-robustness',
            '/cra-fine-and-penalties'
        );

        foreach ($testEndpoints as $endpoint) {
            try {
                $url = JService::$baseUrl . $endpoint;
                Yii::log('Testing endpoint: ' . $url, CLogger::LEVEL_INFO);

                $curl = curl_init($url);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                    "Accept: application/json",
                    "Authorization: Bearer " . Yii::app()->session['token']
                ));
                curl_setopt($curl, CURLOPT_TIMEOUT, 10);

                $response = curl_exec($curl);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                $curlError = curl_error($curl);
                curl_close($curl);

                if ($curlError) {
                    Yii::log('CURL Error for ' . $endpoint . ': ' . $curlError, CLogger::LEVEL_ERROR);
                } else {
                    Yii::log('Response for ' . $endpoint . ' - HTTP: ' . $httpCode . ', Length: ' . strlen($response), CLogger::LEVEL_INFO);
                    if ($httpCode === 200) {
                        $data = json_decode($response);
                        if (is_array($data)) {
                            Yii::log('SUCCESS: ' . $endpoint . ' returned ' . count($data) . ' items', CLogger::LEVEL_INFO);
                        } else {
                            Yii::log('WARNING: ' . $endpoint . ' returned non-array data', CLogger::LEVEL_WARNING);
                        }
                    } else {
                        Yii::log('FAILED: ' . $endpoint . ' returned HTTP ' . $httpCode, CLogger::LEVEL_WARNING);
                    }
                }
            } catch (Exception $e) {
                Yii::log('Exception testing ' . $endpoint . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            }
        }

        Yii::log('=== API CONNECTIVITY TEST COMPLETE ===', CLogger::LEVEL_INFO);
    }

    /**
     * Get all claims for data extraction purposes
     */
    private function getAllClaimsForExtraction() {
        try {
            // Try to get claims from multiple CRA requests to build a comprehensive dataset
            $url = JService::$baseUrl . "/cra-claims";
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200) {
                $claims = json_decode($response);
                return is_array($claims) ? $claims : array();
            }
        } catch (Exception $e) {
            Yii::log('Failed to get claims for extraction: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
        }

        return array();
    }

    /**
     * Process claims data submission
     */
    private function processClaimsData($postData) {
        if (!isset($postData['claimsData']) || empty($postData['claimsData'])) {
            Yii::log('No claims data provided for processing', CLogger::LEVEL_INFO);
            return true; // No claims to process is not an error
        }

        try {
            $claimsData = json_decode($postData['claimsData'], true);
            if (!is_array($claimsData)) {
                Yii::log('Invalid claims data format: not an array', CLogger::LEVEL_ERROR);
                return false;
            }

            $craId = isset($postData['id']) ? intval($postData['id']) : null;
            if (!$craId) {
                Yii::log('No CRA ID provided for claims processing', CLogger::LEVEL_ERROR);
                return false;
            }

            Yii::log('Processing ' . count($claimsData) . ' claims for CRA ID: ' . $craId, CLogger::LEVEL_INFO);

            // First, delete existing claims for this CRA
            $deleteResult = $this->deleteExistingClaims($craId);
            if (!$deleteResult) {
                Yii::log('Failed to delete existing claims for CRA ID: ' . $craId, CLogger::LEVEL_WARNING);
                // Continue processing - deletion failure shouldn't stop new claims creation
            }

            // Then create new claims
            $successCount = 0;
            $totalClaims = count($claimsData);
            foreach ($claimsData as $index => $claimData) {
                if ($this->createClaim($claimData, $craId)) {
                    $successCount++;
                } else {
                    Yii::log('Failed to create claim ' . ($index + 1) . ' for CRA ID: ' . $craId, CLogger::LEVEL_ERROR);
                }
            }

            $success = ($successCount === $totalClaims);
            Yii::log('Claims processing result: ' . $successCount . '/' . $totalClaims . ' claims processed successfully for CRA ID: ' . $craId,
                     $success ? CLogger::LEVEL_INFO : CLogger::LEVEL_ERROR);

            return $success;

        } catch (Exception $e) {
            Yii::log('Error processing claims data: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Delete existing claims for a CRA request
     */
    private function deleteExistingClaims($craId) {
        try {
            $url = JService::$baseUrl . "/cra-claims/by-request/" . $craId;
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200) {
                $existingClaims = json_decode($response, true);
                if (is_array($existingClaims)) {
                    $deleteCount = 0;
                    $totalClaims = count($existingClaims);
                    Yii::log('Found ' . $totalClaims . ' existing claims to delete for CRA ID: ' . $craId, CLogger::LEVEL_INFO);

                    foreach ($existingClaims as $claim) {
                        if (isset($claim['id'])) {
                            if ($this->deleteClaim($claim['id'])) {
                                $deleteCount++;
                            }
                        }
                    }

                    $success = ($deleteCount === $totalClaims);
                    Yii::log('Deleted ' . $deleteCount . '/' . $totalClaims . ' existing claims for CRA ID: ' . $craId,
                             $success ? CLogger::LEVEL_INFO : CLogger::LEVEL_WARNING);
                    return $success;
                } else {
                    Yii::log('No existing claims found for CRA ID: ' . $craId, CLogger::LEVEL_INFO);
                    return true; // No claims to delete is success
                }
            } else {
                Yii::log('Failed to fetch existing claims for CRA ID: ' . $craId . '. HTTP Code: ' . $httpCode, CLogger::LEVEL_ERROR);
                return false;
            }
        } catch (Exception $e) {
            Yii::log('Error deleting existing claims: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Delete a single claim
     */
    private function deleteClaim($claimId) {
        try {
            $url = JService::$baseUrl . "/cra-claims/" . $claimId;
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200 || $httpCode === 204) {
                return true;
            } else {
                Yii::log('Failed to delete claim ' . $claimId . '. HTTP Code: ' . $httpCode . ', Response: ' . $response, CLogger::LEVEL_ERROR);
                return false;
            }
        } catch (Exception $e) {
            Yii::log('Error deleting claim ' . $claimId . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Create a new claim
     */
    private function createClaim($claimData, $craId) {
        try {
            // Validate required claim data
            if (!isset($claimData['claims']) || empty($claimData['claims'])) {
                Yii::log('Cannot create claim: missing claims content', CLogger::LEVEL_ERROR);
                return false;
            }

            $apiData = array(
                'craId' => intval($craId), // Ensure craId is sent as integer
                'claimType' => isset($claimData['claimType']) ? $claimData['claimType'] : 'text',
                'claims' => $claimData['claims'],
                'detail' => isset($claimData['detail']) ? $claimData['detail'] : '',
                'robustnessId' => isset($claimData['robustnessId']) && $claimData['robustnessId'] !== '' ? intval($claimData['robustnessId']) : null,
                'framedRisk' => isset($claimData['framedRisk']) ? $claimData['framedRisk'] : '',
                'criticalRisk' => isset($claimData['criticalRisk']) ? $claimData['criticalRisk'] : '',
                'fineAndPenaltyId' => isset($claimData['fineAndPenaltyId']) && $claimData['fineAndPenaltyId'] !== '' ? intval($claimData['fineAndPenaltyId']) : null
            );

            Yii::log('Creating claim with type: ' . $apiData['claimType'] . ' for CRA ID: ' . $craId, CLogger::LEVEL_INFO);
            Yii::log('API Data: ' . json_encode($apiData), CLogger::LEVEL_INFO);

            $url = JService::$baseUrl . "/cra-claims";
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($apiData));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200 || $httpCode === 201) {
                return true;
            } else {
                Yii::log('Failed to create claim. HTTP Code: ' . $httpCode . ', Response: ' . $response, CLogger::LEVEL_ERROR);
                return false;
            }

        } catch (Exception $e) {
            Yii::log('Error creating claim: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Helper method to get redirect URL based on user role
     * Optimized for code reuse
     */
    private function getRedirectUrlByRole($itemId) {
        if(in_array(Yii::app()->user->groupName, array("SCI","SCI Manager","SCI Staff"))){
            return Yii::app()->createUrl('cra/process', array("id" => $itemId));
        } else {
            return Yii::app()->createUrl('cra/update', array("id" => $itemId));
        }
    }

    /**
     * Helper method to validate submission requirements
     * Optimized for consistent validation
     */
    private function validateSubmissionFields($craForm) {
        $required = array(
            'expositionLevel' => 'Exposition Level',
            'expositionDetail' => 'Exposition Detail',
            'advertisementType' => 'Advertisement Type',
            'timeline' => 'Timeline',
            'requestIds' => 'At least one Request ID'
        );

        foreach ($required as $field => $label) {
            if (empty($craForm->$field)) {
                throw new Exception($label . ' is required for submission');
            }
        }
    }

    /**
     * Helper method to format validation errors
     * Optimized for consistent error handling
     */
    private function formatValidationErrors($craForm) {
        $errors = array();
        foreach ($craForm->getErrors() as $field => $fieldErrors) {
            $errors[] = $field . ': ' . implode(', ', $fieldErrors);
        }
        return 'Validation failed: ' . implode('; ', $errors);
    }
}
