<?php

/**
 * CRA Service XLS Export Helper
 */
class CraServiceXls {
    
    public static function export($fields, $orders = array(), $queries = array(), $query = "", $filter = null) {
        if (isset(Yii::app()->session['token'])) {
            // Map frontend fields to backend enum fields
            $field_mapping = array(
                'craNumber' => 'id',
                'contentFiles' => 'contentFiles',
                'dateOfSubmitting' => 'dateOfSubmitting',
                'dateOfRequestingProofDocument' => 'dateOfRequestingProofDocument',
                'dateOfReceivingProofDocument' => 'dateOfReceivingProofDocument',
                'dateOfStradRiskAssessment' => 'dateOfStradRiskAssessment',
                'dateOfCompleted' => 'dateOfCompleted',
                'exposition' => 'exposition',
                'creatorEmail' => 'creatorEmail',
                'followerEmail' => 'followerEmail',
                'assigneeEmail' => 'assigneeEmail',
                'creatorFullName' => 'creatorFullName',
                'followerFullName' => 'followerFullName',
                'assigneeFullName' => 'assigneeFullName',
                'proofDocuments' => 'proofDocuments',
                'referencesFiles' => 'referenceFiles',
                'requests' => 'requestIds',
                'status' => 'status',
                'stradRiskAssessmentFiles' => 'stradRiskAssessmentFiles',
                'timeline' => 'timeline'
            );

            // Fix fields based on available export fields and map to backend enum
            $fix_fields = array();
            $tmp_fields = explode(",", $fields);
            foreach (array_keys(CraService::$list_export_fields) as $tmp_field) {
                if (in_array($tmp_field, $tmp_fields)) {
                    // Map to backend field name if mapping exists, otherwise use original
                    $backend_field = isset($field_mapping[$tmp_field]) ? $field_mapping[$tmp_field] : $tmp_field;
                    $fix_fields[] = $backend_field;
                }
            }
            $fields = implode(",", $fix_fields);
            
            // Create query
            $query = CraService::createQuery($queries, $query);
            
            $data = array(
                "query" => ($query != "") ? $query : null,
                "order" => (sizeof($orders) > 0) ? implode(",", $orders) : null,
                "filter" => $filter,
                'fields' => $fields
            );
            
            $baseUrl = 'http://localhost:3005/api';
            $curl = curl_init($baseUrl . "/cra-requests/xls?" . http_build_query($data));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_BINARYTRANSFER, true);
            curl_setopt($curl, CURLOPT_HEADER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Authorization: Bearer " . Yii::app()->session['token']
            ));
            
            $response = curl_exec($curl);
            if ($response === FALSE) {
                throw new CHttpException(500, 'Connection Failure.');
            }
            $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            if ($http_status == 200) {
                return $response;
            } elseif ($http_status == 401) {
                throw new CHttpException(403, 'You are not authorized to perform this action.');
            } else {
                throw new CHttpException(500, 'Export failed with status: ' . $http_status);
            }
        } else {
            throw new CHttpException(401, 'Authentication required.');
        }
    }
}
